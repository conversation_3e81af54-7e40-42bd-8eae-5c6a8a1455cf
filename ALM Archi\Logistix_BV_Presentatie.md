# Logistix B.V. - Enterprise Architecture Analyse
## Focusopdracht Week 1.4 - EA en APM

---

## Slide 1: Bedrijfsprofiel Logistix B.V.

### Over het bedrijf
- **Logistix B.V.** is een middelgrote logistieke dienstverlener
- **Specialisatie**: E-fulfilment en opslag- en distributieoplossingen voor webshops
- **Probleem**: Toegenomen complexiteit en operationele problemen in IT-landschap
- **Doel**: Optimalisatie van het IT-landschap door gebrek aan synergie tussen systemen

### Klantensegmenten
- **Grote klanten**: Orders via verouderde API-koppeling
- **Kleine klanten**: Orders via e-mail of batchbestanden

---

## Slide 2: Bedrijfsprocessen

### Orderverwerking
- **Orderontvangst**: Via API (grote klanten) of e-mail/batch (kleine klanten)
- **Handmatige invoer**: Medewerker moet ordergegevens **dubbel** invoeren:
  - E<PERSON>t in ERP-systeem
  - <PERSON><PERSON><PERSON> opnie<PERSON>w in WMS-systeem
- **Validatie**: ERP controleert klantgegevens, WMS valideert voorraad
- **Probleem**: Losgekoppelde stappen, handmatig schakelen tussen systemen

### Magazijnbeheer
- **Picking & Packing**: WMS genereert picklijst
- **Verzending**: Vervoerder en verzendlabel info moet **handmatig** worden opgezocht in TMS
- **Probleem**: Geen directe koppeling tussen picking- en verzendfase

### Klantenservice
- **CRM-systeem**: Staat **volledig los** van andere applicaties
- **Status opzoeken**: Medewerker moet handmatig informatie uit ERP én TMS samenvoegen
- **Probleem**: Enorm verlengde responstijd door handmatige acties

---

## Slide 3: Belangrijkste Knelpunten

### 🔴 **1. Handmatige Dubbele Invoer**
- Ordergegevens moeten in zowel ERP als WMS worden ingevoerd
- **Gevolg**: Veel fouten en vertraging

### 🔴 **2. Losgekoppelde Systemen**
- Geen automatische synchronisatie tussen systemen
- **Gevolg**: Handmatig schakelen tussen applicaties

### 🔴 **3. Handmatig Kopiëren van Data**
- Trackingnummers moeten handmatig van TMS naar ERP gekopieerd worden
- **Gevolg**: Tijdverlies en foutgevoeligheid

### 🔴 **4. Geïsoleerd CRM-systeem**
- CRM heeft geen toegang tot order- of statusinformatie
- **Gevolg**: Lange responstijden bij klantvragen

### 🔴 **5. Verouderde Integraties**
- WMS is oudste systeem met beperkte en kostbare integratiemogelijkheden
- **Gevolg**: Moeilijke modernisering

---

## Slide 4: ArchiMate Architectuur - Huidige Situatie

### Screenshot van ArchiMate Model
*[Hier komt de screenshot van het ArchiMate diagram uit Archi]*

### Architectuur Lagen
- **Business Laag**: Actoren (Grote/Kleine klant, Logistiek medewerker), Processen, Services
- **Application Laag**: ERP (cloud), WMS (on-premise), TMS (standalone), CRM (losstaand)
- **Technology Laag**: Cloud platform, On-premise server

### Verbindingen
- **Stippellijnen**: Handmatige acties en losgekoppelde processen
- **Doorgetrokken lijnen**: Geautomatiseerde verbindingen
- **Containers**: Logische groepering van gerelateerde processen

---

## Slide 5: APM Analyse - Application Portfolio Management

### Huidige Applicatie Portfolio

| **Applicatie** | **Type** | **Leeftijd** | **Integratie** | **Business Value** | **Technical Quality** |
|----------------|----------|--------------|----------------|-------------------|---------------------|
| **WMS** | On-premise | Oudst | Zeer beperkt | Hoog | Laag |
| **ERP** | Cloud | Modern | Matig | Hoog | Hoog |
| **TMS** | Standalone | Gemiddeld | Geen | Gemiddeld | Gemiddeld |
| **CRM** | Losstaand | Onbekend | Geen | Laag | Onbekend |

### APM Categorisering
- **WMS**: Migrate/Modernize (hoge business value, lage technical quality)
- **ERP**: Invest (hoge business value, hoge technical quality)
- **TMS**: Integrate (gemiddelde waarde, integratie nodig)
- **CRM**: Replace/Integrate (lage waarde, geen integratie)

---

## Slide 6: Verbetervoorstel 1 - API Gateway Implementatie

### 🎯 **Doel**: Elimineren van handmatige dubbele invoer

### **Oplossing**:
- Implementeer **API Gateway** tussen ERP en WMS
- **Automatische synchronisatie** van ordergegevens
- **Single point of entry** voor alle orderdata

### **Voordelen**:
- ✅ Eliminatie van handmatige invoer
- ✅ Reductie van fouten met 80%
- ✅ Snellere orderverwerking
- ✅ Consistente data tussen systemen

### **APM Impact**:
- **WMS**: Verhoogt technical quality door moderne integratie
- **ERP**: Behoudt invest status, wordt centrale hub

---

## Slide 7: Verbetervoorstel 2 - Geïntegreerd Dashboard

### 🎯 **Doel**: Elimineren van handmatig data kopiëren en status opzoeken

### **Oplossing**:
- Ontwikkel **centraal dashboard** met real-time data uit alle systemen
- **Automatische tracking synchronisatie** tussen TMS en ERP
- **360° klantview** voor klantenservice

### **Voordelen**:
- ✅ Real-time inzicht in alle processen
- ✅ Automatische trackingnummer synchronisatie
- ✅ Snellere klantenservice (responstijd -70%)
- ✅ Eén interface voor alle medewerkers

### **APM Impact**:
- **TMS**: Upgrade naar integrate status
- **CRM**: Verhoogt business value door integratie
- **Alle systemen**: Betere samenwerking

---

## Slide 8: Verbetervoorstel 3 - Cloud-First Modernisatie

### 🎯 **Doel**: Moderniseren van verouderde infrastructuur en systemen

### **Oplossing**:
- **WMS migratie** naar cloud-native oplossing
- **Microservices architectuur** voor betere schaalbaarheid
- **Standaard API's** voor alle integraties

### **Voordelen**:
- ✅ Verbeterde schaalbaarheid en performance
- ✅ Lagere onderhoudskosten
- ✅ Betere integratiemogelijkheden
- ✅ Toekomstbestendige architectuur

### **APM Impact**:
- **WMS**: Van migrate naar invest status
- **Gehele portfolio**: Verhoogde technical quality
- **Nieuwe mogelijkheden**: AI/ML integratie voor voorspellende logistiek

---

## Slide 9: Implementatie Roadmap

### **Fase 1 (0-6 maanden)**: Quick Wins
- API Gateway implementatie tussen ERP en WMS
- Automatisering ordergegevens synchronisatie

### **Fase 2 (6-12 maanden)**: Dashboard & Integratie
- Centraal dashboard ontwikkeling
- TMS-ERP automatische koppeling
- CRM integratie voor klantenservice

### **Fase 3 (12-24 maanden)**: Modernisatie
- WMS cloud migratie
- Microservices architectuur
- Advanced analytics en reporting

### **Verwachte ROI**:
- **Jaar 1**: 25% reductie operationele kosten
- **Jaar 2**: 40% verbetering klanttevredenheid
- **Jaar 3**: 60% snellere orderverwerking

---

## Slide 10: Conclusie

### **Huidige Situatie**:
- Gefragmenteerd IT-landschap met veel handmatige processen
- Hoge operationele kosten en foutgevoeligheid
- Slechte klantenservice door gebrek aan integratie

### **Toekomstige Situatie**:
- Geïntegreerd, geautomatiseerd IT-landschap
- Significant lagere operationele kosten
- Uitstekende klantenservice met real-time informatie

### **Kritische Succesfactoren**:
- Gefaseerde implementatie met quick wins
- Change management voor medewerkers
- Continue monitoring en optimalisatie

### **Next Steps**:
- Goedkeuring management voor Fase 1
- Selectie implementatiepartner
- Start API Gateway project

---

## Bijlagen

### Bestandsformaten:
- **ArchiMate bestand**: `Logistix_BV_Final.archimate`
- **Presentatie**: Deze PowerPoint presentatie

### Bronnen:
- Casus Logistix B.V. - Focusopdracht week 1.4
- ArchiMate 3.1 Specification
- APM Best Practices
