<?xml version="1.0" encoding="UTF-8"?>
<archimate:model xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:archimate="http://www.archimatetool.com/archimate" name="Logistix BV - E-fulfilment en Distributie" id="id-logistix-model" version="5.0.0">
  <folder name="Strategy" id="id-strategy-folder" type="strategy"/>
  <folder name="Business" id="id-business-folder" type="business">
    <element xsi:type="archimate:BusinessActor" name="Grote klant" id="id-actor-grote-klant"/>
    <element xsi:type="archimate:BusinessActor" name="Kleine klant" id="id-actor-kleine-klant"/>
    <element xsi:type="archimate:BusinessActor" name="Logistiek medewerker" id="id-actor-logistiek-medewerker"/>
    
    <element xsi:type="archimate:BusinessRole" name="Orderverwerker" id="id-role-orderverwerker"/>
    
    <element xsi:type="archimate:BusinessService" name="E-fulfilment dienstverlening" id="id-service-efulfilment"/>
    
    <element xsi:type="archimate:BusinessProcess" name="Order verwerken" id="id-process-order-verwerken"/>
    <element xsi:type="archimate:BusinessProcess" name="Order invoeren" id="id-process-order-invoeren"/>
    <element xsi:type="archimate:BusinessProcess" name="Klant controleren" id="id-process-klant-controleren"/>
    <element xsi:type="archimate:BusinessProcess" name="Voorraad check" id="id-process-voorraad-check"/>
    <element xsi:type="archimate:BusinessProcess" name="Picking &amp; Packing" id="id-process-picking-packing"/>
    <element xsi:type="archimate:BusinessProcess" name="Verzendlabel maken" id="id-process-verzendlabel-maken"/>
    <element xsi:type="archimate:BusinessProcess" name="Tracking kopiëren" id="id-process-tracking-kopieren"/>
    <element xsi:type="archimate:BusinessProcess" name="Klantcommunicatie" id="id-process-klantcommunicatie"/>
  </folder>
  <folder name="Application" id="id-application-folder" type="application">
    <element xsi:type="archimate:ApplicationService" name="Orderverwerking" id="id-app-service-orderverwerking"/>
    
    <element xsi:type="archimate:ApplicationComponent" name="ERP (cloud)" id="id-app-erp"/>
    <element xsi:type="archimate:ApplicationComponent" name="WMS (on-premise)" id="id-app-wms"/>
    <element xsi:type="archimate:ApplicationComponent" name="TMS (standalone)" id="id-app-tms"/>
    <element xsi:type="archimate:ApplicationComponent" name="CRM (losstaand)" id="id-app-crm"/>
    
    <element xsi:type="archimate:DataObject" name="Ordergegevens" id="id-data-ordergegevens"/>
    <element xsi:type="archimate:DataObject" name="Klantgegevens" id="id-data-klantgegevens"/>
    <element xsi:type="archimate:DataObject" name="Voorraadgegevens" id="id-data-voorraadgegevens"/>
    <element xsi:type="archimate:DataObject" name="Transportgegevens" id="id-data-transportgegevens"/>
  </folder>
  <folder name="Technology &amp; Physical" id="id-technology-folder" type="technology">
    <element xsi:type="archimate:Node" name="Cloud platform" id="id-node-cloud"/>
    <element xsi:type="archimate:Node" name="On-premise server" id="id-node-onpremise"/>
    <element xsi:type="archimate:CommunicationNetwork" name="Netwerk (internet)" id="id-network-internet"/>
  </folder>
  <folder name="Motivation" id="id-motivation-folder" type="motivation"/>
  <folder name="Implementation &amp; Migration" id="id-implementation-folder" type="implementation_migration"/>
  <folder name="Other" id="id-other-folder" type="other"/>
  <folder name="Relations" id="id-relations-folder" type="relations">
    <!-- Business Actor to Process -->
    <element xsi:type="archimate:TriggeringRelationship" id="id-rel-grote-klant-order" source="id-actor-grote-klant" target="id-process-order-verwerken"/>
    <element xsi:type="archimate:TriggeringRelationship" id="id-rel-kleine-klant-order" source="id-actor-kleine-klant" target="id-process-order-verwerken"/>
    <element xsi:type="archimate:AssignmentRelationship" id="id-rel-logistiek-orderverwerker" source="id-actor-logistiek-medewerker" target="id-role-orderverwerker"/>
    <element xsi:type="archimate:ServingRelationship" id="id-rel-orderverwerker-process" source="id-role-orderverwerker" target="id-process-order-verwerken"/>
    
    <!-- Process Composition (sub-processes) -->
    <element xsi:type="archimate:CompositionRelationship" id="id-rel-order-invoeren-comp" source="id-process-order-verwerken" target="id-process-order-invoeren"/>
    <element xsi:type="archimate:CompositionRelationship" id="id-rel-klant-controleren-comp" source="id-process-order-verwerken" target="id-process-klant-controleren"/>
    <element xsi:type="archimate:CompositionRelationship" id="id-rel-voorraad-check-comp" source="id-process-order-verwerken" target="id-process-voorraad-check"/>
    <element xsi:type="archimate:CompositionRelationship" id="id-rel-picking-packing-comp" source="id-process-order-verwerken" target="id-process-picking-packing"/>
    <element xsi:type="archimate:CompositionRelationship" id="id-rel-verzendlabel-comp" source="id-process-order-verwerken" target="id-process-verzendlabel-maken"/>
    <element xsi:type="archimate:CompositionRelationship" id="id-rel-tracking-comp" source="id-process-order-verwerken" target="id-process-tracking-kopieren"/>
    
    <!-- Application Realization of Processes -->
    <element xsi:type="archimate:RealizationRelationship" id="id-rel-erp-order-invoeren" source="id-app-erp" target="id-process-order-invoeren"/>
    <element xsi:type="archimate:RealizationRelationship" id="id-rel-erp-klant-controleren" source="id-app-erp" target="id-process-klant-controleren"/>
    <element xsi:type="archimate:RealizationRelationship" id="id-rel-wms-voorraad-check" source="id-app-wms" target="id-process-voorraad-check"/>
    <element xsi:type="archimate:RealizationRelationship" id="id-rel-wms-picking-packing" source="id-app-wms" target="id-process-picking-packing"/>
    <element xsi:type="archimate:RealizationRelationship" id="id-rel-tms-verzendlabel" source="id-app-tms" target="id-process-verzendlabel-maken"/>
    <element xsi:type="archimate:RealizationRelationship" id="id-rel-tms-tracking" source="id-app-tms" target="id-process-tracking-kopieren"/>
    <element xsi:type="archimate:RealizationRelationship" id="id-rel-crm-klantcommunicatie" source="id-app-crm" target="id-process-klantcommunicatie"/>
    
    <!-- Technology Realization -->
    <element xsi:type="archimate:RealizationRelationship" id="id-rel-cloud-erp" source="id-node-cloud" target="id-app-erp"/>
    <element xsi:type="archimate:RealizationRelationship" id="id-rel-onpremise-wms" source="id-node-onpremise" target="id-app-wms"/>
    <element xsi:type="archimate:RealizationRelationship" id="id-rel-onpremise-tms" source="id-node-onpremise" target="id-app-tms"/>
    <element xsi:type="archimate:RealizationRelationship" id="id-rel-onpremise-crm" source="id-node-onpremise" target="id-app-crm"/>
    
    <!-- Network connections -->
    <element xsi:type="archimate:AssociationRelationship" id="id-rel-cloud-network" source="id-node-cloud" target="id-network-internet"/>
    <element xsi:type="archimate:AssociationRelationship" id="id-rel-onpremise-network" source="id-node-onpremise" target="id-network-internet"/>
    
    <!-- Data Access -->
    <element xsi:type="archimate:AccessRelationship" id="id-rel-erp-ordergegevens" source="id-app-erp" target="id-data-ordergegevens"/>
    <element xsi:type="archimate:AccessRelationship" id="id-rel-erp-klantgegevens" source="id-app-erp" target="id-data-klantgegevens"/>
    <element xsi:type="archimate:AccessRelationship" id="id-rel-wms-voorraadgegevens" source="id-app-wms" target="id-data-voorraadgegevens"/>
    <element xsi:type="archimate:AccessRelationship" id="id-rel-tms-transportgegevens" source="id-app-tms" target="id-data-transportgegevens"/>
    
    <!-- Business Service Realization -->
    <element xsi:type="archimate:RealizationRelationship" id="id-rel-process-service" source="id-process-order-verwerken" target="id-service-efulfilment"/>
  </folder>
  <folder name="Views" id="id-views-folder" type="diagrams">
    <element xsi:type="archimate:ArchimateDiagramModel" name="Logistix BV - Huidige Situatie" id="id-view-current-state">
      <!-- Business Actors - Top Row -->
      <child xsi:type="archimate:DiagramObject" id="id-diagram-grote-klant" archimateElement="id-actor-grote-klant">
        <bounds x="144" y="120" width="120" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-grote-klant-order" source="id-diagram-grote-klant" target="id-diagram-order-verwerken" archimateRelationship="id-rel-grote-klant-order"/>
      </child>

      <child xsi:type="archimate:DiagramObject" id="id-diagram-kleine-klant" archimateElement="id-actor-kleine-klant">
        <bounds x="300" y="120" width="120" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-kleine-klant-order" source="id-diagram-kleine-klant" target="id-diagram-order-verwerken" archimateRelationship="id-rel-kleine-klant-order"/>
      </child>

      <child xsi:type="archimate:DiagramObject" id="id-diagram-logistiek-medewerker" archimateElement="id-actor-logistiek-medewerker">
        <bounds x="300" y="200" width="120" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-logistiek-orderverwerker" source="id-diagram-logistiek-medewerker" target="id-diagram-orderverwerker" archimateRelationship="id-rel-logistiek-orderverwerker"/>
      </child>

      <!-- Business Role -->
      <child xsi:type="archimate:DiagramObject" id="id-diagram-orderverwerker" targetConnections="id-conn-logistiek-orderverwerker" archimateElement="id-role-orderverwerker">
        <bounds x="311" y="280" width="133" height="44"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-orderverwerker-process" source="id-diagram-orderverwerker" target="id-diagram-order-verwerken" archimateRelationship="id-rel-orderverwerker-process"/>
      </child>

      <!-- Business Service -->
      <child xsi:type="archimate:DiagramObject" id="id-diagram-service-efulfilment" targetConnections="id-conn-process-service" archimateElement="id-service-efulfilment">
        <bounds x="415" y="50" width="168" height="55"/>
      </child>

      <!-- Main Business Process Container -->
      <child xsi:type="archimate:DiagramObject" id="id-diagram-order-verwerken" targetConnections="id-conn-grote-klant-order id-conn-kleine-klant-order id-conn-orderverwerker-process" archimateElement="id-process-order-verwerken">
        <bounds x="168" y="350" width="661" height="85"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-process-service" source="id-diagram-order-verwerken" target="id-diagram-service-efulfilment" archimateRelationship="id-rel-process-service"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-order-invoeren-comp" source="id-diagram-order-verwerken" target="id-diagram-order-invoeren" archimateRelationship="id-rel-order-invoeren-comp"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-klant-controleren-comp" source="id-diagram-order-verwerken" target="id-diagram-klant-controleren" archimateRelationship="id-rel-klant-controleren-comp"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-voorraad-check-comp" source="id-diagram-order-verwerken" target="id-diagram-voorraad-check" archimateRelationship="id-rel-voorraad-check-comp"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-picking-packing-comp" source="id-diagram-order-verwerken" target="id-diagram-picking-packing" archimateRelationship="id-rel-picking-packing-comp"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-verzendlabel-comp" source="id-diagram-order-verwerken" target="id-diagram-verzendlabel-maken" archimateRelationship="id-rel-verzendlabel-comp"/>

        <!-- Sub-processes inside container -->
        <child xsi:type="archimate:DiagramObject" id="id-diagram-order-invoeren" targetConnections="id-conn-order-invoeren-comp id-conn-erp-order-invoeren" archimateElement="id-process-order-invoeren">
          <bounds x="20" y="24" width="120" height="55"/>
        </child>

        <child xsi:type="archimate:DiagramObject" id="id-diagram-klant-controleren" targetConnections="id-conn-klant-controleren-comp id-conn-erp-klant-controleren" archimateElement="id-process-klant-controleren">
          <bounds x="160" y="24" width="120" height="55"/>
        </child>

        <child xsi:type="archimate:DiagramObject" id="id-diagram-voorraad-check" targetConnections="id-conn-voorraad-check-comp id-conn-wms-voorraad-check" archimateElement="id-process-voorraad-check">
          <bounds x="300" y="24" width="120" height="55"/>
        </child>

        <child xsi:type="archimate:DiagramObject" id="id-diagram-picking-packing" targetConnections="id-conn-picking-packing-comp id-conn-wms-picking-packing" archimateElement="id-process-picking-packing">
          <bounds x="440" y="24" width="120" height="55"/>
        </child>

        <child xsi:type="archimate:DiagramObject" id="id-diagram-verzendlabel-maken" targetConnections="id-conn-verzendlabel-comp id-conn-tms-verzendlabel" archimateElement="id-process-verzendlabel-maken">
          <bounds x="580" y="24" width="120" height="55"/>
        </child>
      </child>

      <!-- Separate Processes -->
      <child xsi:type="archimate:DiagramObject" id="id-diagram-tracking-kopieren" archimateElement="id-process-tracking-kopieren">
        <bounds x="600" y="280" width="120" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-tracking-comp" source="id-diagram-order-verwerken" target="id-diagram-tracking-kopieren" archimateRelationship="id-rel-tracking-comp"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-tms-tracking" source="id-diagram-tms" target="id-diagram-tracking-kopieren" archimateRelationship="id-rel-tms-tracking"/>
      </child>

      <child xsi:type="archimate:DiagramObject" id="id-diagram-klantcommunicatie" archimateElement="id-process-klantcommunicatie">
        <bounds x="750" y="280" width="120" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-crm-klantcommunicatie" source="id-diagram-crm" target="id-diagram-klantcommunicatie" archimateRelationship="id-rel-crm-klantcommunicatie"/>
      </child>

      <!-- Application Service Container -->
      <child xsi:type="archimate:DiagramObject" id="id-diagram-app-service-orderverwerking" archimateElement="id-app-service-orderverwerking">
        <bounds x="36" y="480" width="829" height="181"/>

        <!-- ERP System -->
        <child xsi:type="archimate:DiagramObject" id="id-diagram-erp" archimateElement="id-app-erp">
          <bounds x="50" y="36" width="150" height="55"/>
          <sourceConnection xsi:type="archimate:Connection" id="id-conn-erp-order-invoeren" source="id-diagram-erp" target="id-diagram-order-invoeren" archimateRelationship="id-rel-erp-order-invoeren"/>
          <sourceConnection xsi:type="archimate:Connection" id="id-conn-erp-klant-controleren" source="id-diagram-erp" target="id-diagram-klant-controleren" archimateRelationship="id-rel-erp-klant-controleren"/>
        </child>

        <!-- WMS System -->
        <child xsi:type="archimate:DiagramObject" id="id-diagram-wms" archimateElement="id-app-wms">
          <bounds x="220" y="36" width="150" height="55"/>
          <sourceConnection xsi:type="archimate:Connection" id="id-conn-wms-voorraad-check" source="id-diagram-wms" target="id-diagram-voorraad-check" archimateRelationship="id-rel-wms-voorraad-check"/>
          <sourceConnection xsi:type="archimate:Connection" id="id-conn-wms-picking-packing" source="id-diagram-wms" target="id-diagram-picking-packing" archimateRelationship="id-rel-wms-picking-packing"/>
        </child>

        <!-- TMS System -->
        <child xsi:type="archimate:DiagramObject" id="id-diagram-tms" archimateElement="id-app-tms">
          <bounds x="390" y="36" width="150" height="55"/>
          <sourceConnection xsi:type="archimate:Connection" id="id-conn-tms-verzendlabel" source="id-diagram-tms" target="id-diagram-verzendlabel-maken" archimateRelationship="id-rel-tms-verzendlabel"/>
        </child>

        <!-- CRM System -->
        <child xsi:type="archimate:DiagramObject" id="id-diagram-crm" archimateElement="id-app-crm">
          <bounds x="560" y="36" width="150" height="55"/>
        </child>
      </child>

      <!-- Data Objects -->
      <child xsi:type="archimate:DiagramObject" id="id-diagram-ordergegevens" archimateElement="id-data-ordergegevens">
        <bounds x="100" y="700" width="144" height="60"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-erp-ordergegevens" source="id-diagram-erp" target="id-diagram-ordergegevens" archimateRelationship="id-rel-erp-ordergegevens"/>
      </child>

      <child xsi:type="archimate:DiagramObject" id="id-diagram-klantgegevens" archimateElement="id-data-klantgegevens">
        <bounds x="270" y="700" width="144" height="60"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-erp-klantgegevens" source="id-diagram-erp" target="id-diagram-klantgegevens" archimateRelationship="id-rel-erp-klantgegevens"/>
      </child>

      <child xsi:type="archimate:DiagramObject" id="id-diagram-voorraadgegevens" archimateElement="id-data-voorraadgegevens">
        <bounds x="440" y="700" width="144" height="60"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-wms-voorraadgegevens" source="id-diagram-wms" target="id-diagram-voorraadgegevens" archimateRelationship="id-rel-wms-voorraadgegevens"/>
      </child>

      <child xsi:type="archimate:DiagramObject" id="id-diagram-transportgegevens" archimateElement="id-data-transportgegevens">
        <bounds x="610" y="700" width="144" height="60"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-tms-transportgegevens" source="id-diagram-tms" target="id-diagram-transportgegevens" archimateRelationship="id-rel-tms-transportgegevens"/>
      </child>

      <!-- Technology Layer -->
      <child xsi:type="archimate:DiagramObject" id="id-diagram-cloud" archimateElement="id-node-cloud">
        <bounds x="168" y="720" width="300" height="49"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-cloud-erp" source="id-diagram-cloud" target="id-diagram-erp" archimateRelationship="id-rel-cloud-erp"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-cloud-network" source="id-diagram-cloud" target="id-diagram-network" archimateRelationship="id-rel-cloud-network"/>
      </child>

      <child xsi:type="archimate:DiagramObject" id="id-diagram-onpremise" archimateElement="id-node-onpremise">
        <bounds x="500" y="720" width="300" height="49"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-onpremise-wms" source="id-diagram-onpremise" target="id-diagram-wms" archimateRelationship="id-rel-onpremise-wms"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-onpremise-tms" source="id-diagram-onpremise" target="id-diagram-tms" archimateRelationship="id-rel-onpremise-tms"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-onpremise-crm" source="id-diagram-onpremise" target="id-diagram-crm" archimateRelationship="id-rel-onpremise-crm"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-onpremise-network" source="id-diagram-onpremise" target="id-diagram-network" archimateRelationship="id-rel-onpremise-network"/>
      </child>

      <!-- Network -->
      <child xsi:type="archimate:DiagramObject" id="id-diagram-network" targetConnections="id-conn-cloud-network id-conn-onpremise-network" archimateElement="id-network-internet">
        <bounds x="156" y="800" width="660" height="48"/>
      </child>
    </element>
  </folder>
</archimate:model>
