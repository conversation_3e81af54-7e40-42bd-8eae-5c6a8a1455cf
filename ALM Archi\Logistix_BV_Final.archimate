<?xml version="1.0" encoding="UTF-8"?>
<archimate:model xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:archimate="http://www.archimatetool.com/archimate" name="Logistix BV - E-fulfilment en Distributie" id="id-logistix-final-model" version="5.0.0">
  <folder name="Strategy" id="id-strategy-folder" type="strategy"/>
  <folder name="Business" id="id-business-folder" type="business">
    <element xsi:type="archimate:BusinessActor" name="Grote klant" id="id-actor-grote-klant"/>
    <element xsi:type="archimate:BusinessActor" name="Kleine klant" id="id-actor-kleine-klant"/>
    <element xsi:type="archimate:BusinessActor" name="Logistiek medewerker" id="id-actor-logistiek-medewerker"/>
    
    <element xsi:type="archimate:BusinessRole" name="Orderverwerker" id="id-role-orderverwerker"/>
    
    <element xsi:type="archimate:BusinessService" name="E-fulfilment dienstverlening" id="id-service-efulfilment"/>
    
    <element xsi:type="archimate:BusinessProcess" name="Order ontvangen" id="id-process-order-ontvangen"/>
    <element xsi:type="archimate:BusinessProcess" name="Order invoeren" id="id-process-order-invoeren"/>
    <element xsi:type="archimate:BusinessProcess" name="Klant controleren" id="id-process-klant-controleren"/>
    <element xsi:type="archimate:BusinessProcess" name="Voorraad check" id="id-process-voorraad-check"/>
    <element xsi:type="archimate:BusinessProcess" name="Picking &amp; Packing" id="id-process-picking-packing"/>
    <element xsi:type="archimate:BusinessProcess" name="Verzendlabel maken" id="id-process-verzendlabel-maken"/>
    <element xsi:type="archimate:BusinessProcess" name="Tracking kopiëren" id="id-process-tracking-kopieren"/>
    <element xsi:type="archimate:BusinessProcess" name="Klantcommunicatie" id="id-process-klantcommunicatie"/>
  </folder>
  <folder name="Application" id="id-application-folder" type="application">
    <element xsi:type="archimate:ApplicationComponent" name="ERP" id="id-app-erp"/>
    <element xsi:type="archimate:ApplicationComponent" name="WMS" id="id-app-wms"/>
    <element xsi:type="archimate:ApplicationComponent" name="TMS" id="id-app-tms"/>
    <element xsi:type="archimate:ApplicationComponent" name="CRM" id="id-app-crm"/>
  </folder>
  <folder name="Technology &amp; Physical" id="id-technology-folder" type="technology">
    <element xsi:type="archimate:Node" name="Cloud platform" id="id-node-cloud"/>
    <element xsi:type="archimate:Node" name="On-premise server" id="id-node-onpremise"/>
    <element xsi:type="archimate:Node" name="Standalone node" id="id-node-standalone-tms"/>
    <element xsi:type="archimate:Node" name="Standalone node" id="id-node-standalone-crm"/>
  </folder>
  <folder name="Motivation" id="id-motivation-folder" type="motivation"/>
  <folder name="Implementation &amp; Migration" id="id-implementation-folder" type="implementation_migration"/>
  <folder name="Other" id="id-other-folder" type="other"/>
  <folder name="Relations" id="id-relations-folder" type="relations">
    <!-- Triggering relationships -->
    <element xsi:type="archimate:TriggeringRelationship" id="id-rel-grote-klant-order" source="id-actor-grote-klant" target="id-process-order-ontvangen"/>
    <element xsi:type="archimate:TriggeringRelationship" id="id-rel-kleine-klant-order" source="id-actor-kleine-klant" target="id-process-order-ontvangen"/>
    <element xsi:type="archimate:AssignmentRelationship" id="id-rel-logistiek-orderverwerker" source="id-actor-logistiek-medewerker" target="id-role-orderverwerker"/>
    <element xsi:type="archimate:ServingRelationship" id="id-rel-orderverwerker-invoeren" source="id-role-orderverwerker" target="id-process-order-invoeren"/>
    
    <!-- Process flow -->
    <element xsi:type="archimate:TriggeringRelationship" id="id-rel-order-invoeren" source="id-process-order-ontvangen" target="id-process-order-invoeren"/>
    <element xsi:type="archimate:TriggeringRelationship" id="id-rel-invoeren-klant" source="id-process-order-invoeren" target="id-process-klant-controleren"/>
    <element xsi:type="archimate:TriggeringRelationship" id="id-rel-klant-voorraad" source="id-process-klant-controleren" target="id-process-voorraad-check"/>
    <element xsi:type="archimate:TriggeringRelationship" id="id-rel-voorraad-picking" source="id-process-voorraad-check" target="id-process-picking-packing"/>
    <element xsi:type="archimate:TriggeringRelationship" id="id-rel-picking-verzendlabel" source="id-process-picking-packing" target="id-process-verzendlabel-maken"/>
    <element xsi:type="archimate:TriggeringRelationship" id="id-rel-verzendlabel-tracking" source="id-process-verzendlabel-maken" target="id-process-tracking-kopieren"/>
    <element xsi:type="archimate:TriggeringRelationship" id="id-rel-tracking-communicatie" source="id-process-tracking-kopieren" target="id-process-klantcommunicatie"/>
    
    <!-- Used By relationships (Application serves Process) -->
    <element xsi:type="archimate:ServingRelationship" id="id-rel-erp-invoeren" source="id-app-erp" target="id-process-order-invoeren"/>
    <element xsi:type="archimate:ServingRelationship" id="id-rel-erp-klant" source="id-app-erp" target="id-process-klant-controleren"/>
    <element xsi:type="archimate:ServingRelationship" id="id-rel-wms-voorraad" source="id-app-wms" target="id-process-voorraad-check"/>
    <element xsi:type="archimate:ServingRelationship" id="id-rel-wms-picking" source="id-app-wms" target="id-process-picking-packing"/>
    <element xsi:type="archimate:ServingRelationship" id="id-rel-tms-verzendlabel" source="id-app-tms" target="id-process-verzendlabel-maken"/>
    <element xsi:type="archimate:AccessRelationship" id="id-rel-tms-tracking" source="id-process-tracking-kopieren" target="id-app-tms"/>
    <element xsi:type="archimate:ServingRelationship" id="id-rel-crm-communicatie" source="id-app-crm" target="id-process-klantcommunicatie"/>
    
    <!-- Deployment relationships -->
    <element xsi:type="archimate:AssignmentRelationship" id="id-rel-erp-cloud" source="id-app-erp" target="id-node-cloud"/>
    <element xsi:type="archimate:AssignmentRelationship" id="id-rel-wms-onpremise" source="id-app-wms" target="id-node-onpremise"/>
    <element xsi:type="archimate:AssignmentRelationship" id="id-rel-tms-standalone" source="id-app-tms" target="id-node-standalone-tms"/>
    <element xsi:type="archimate:AssignmentRelationship" id="id-rel-crm-standalone" source="id-app-crm" target="id-node-standalone-crm"/>
  </folder>
  <folder name="Views" id="id-views-folder" type="diagrams">
    <element xsi:type="archimate:ArchimateDiagramModel" name="Logistix BV - Huidige Situatie" id="id-view-current-state">
      <!-- Business Actors - Top Row -->
      <child xsi:type="archimate:DiagramObject" id="id-diagram-grote-klant" archimateElement="id-actor-grote-klant">
        <bounds x="50" y="50" width="120" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-grote-klant-order" source="id-diagram-grote-klant" target="id-diagram-order-ontvangen" archimateRelationship="id-rel-grote-klant-order"/>
      </child>

      <child xsi:type="archimate:DiagramObject" id="id-diagram-kleine-klant" archimateElement="id-actor-kleine-klant">
        <bounds x="200" y="50" width="120" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-kleine-klant-order" source="id-diagram-kleine-klant" target="id-diagram-order-ontvangen" archimateRelationship="id-rel-kleine-klant-order"/>
      </child>

      <child xsi:type="archimate:DiagramObject" id="id-diagram-logistiek-medewerker" archimateElement="id-actor-logistiek-medewerker">
        <bounds x="350" y="50" width="120" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-logistiek-orderverwerker" source="id-diagram-logistiek-medewerker" target="id-diagram-orderverwerker" archimateRelationship="id-rel-logistiek-orderverwerker"/>
      </child>

      <!-- Business Role -->
      <child xsi:type="archimate:DiagramObject" id="id-diagram-orderverwerker" targetConnections="id-conn-logistiek-orderverwerker" archimateElement="id-role-orderverwerker">
        <bounds x="350" y="130" width="120" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-orderverwerker-invoeren" source="id-diagram-orderverwerker" target="id-diagram-order-invoeren" archimateRelationship="id-rel-orderverwerker-invoeren"/>
      </child>

      <!-- Business Process Flow - Vertical -->
      <child xsi:type="archimate:DiagramObject" id="id-diagram-order-ontvangen" targetConnections="id-conn-grote-klant-order id-conn-kleine-klant-order" archimateElement="id-process-order-ontvangen">
        <bounds x="125" y="150" width="120" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-order-invoeren" source="id-diagram-order-ontvangen" target="id-diagram-order-invoeren" archimateRelationship="id-rel-order-invoeren"/>
      </child>

      <child xsi:type="archimate:DiagramObject" id="id-diagram-order-invoeren" targetConnections="id-conn-order-invoeren id-conn-orderverwerker-invoeren" archimateElement="id-process-order-invoeren">
        <bounds x="125" y="230" width="120" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-invoeren-klant" source="id-diagram-order-invoeren" target="id-diagram-klant-controleren" archimateRelationship="id-rel-invoeren-klant"/>
      </child>

      <child xsi:type="archimate:DiagramObject" id="id-diagram-klant-controleren" targetConnections="id-conn-invoeren-klant" archimateElement="id-process-klant-controleren">
        <bounds x="125" y="310" width="120" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-klant-voorraad" source="id-diagram-klant-controleren" target="id-diagram-voorraad-check" archimateRelationship="id-rel-klant-voorraad"/>
      </child>

      <child xsi:type="archimate:DiagramObject" id="id-diagram-voorraad-check" targetConnections="id-conn-klant-voorraad" archimateElement="id-process-voorraad-check">
        <bounds x="125" y="390" width="120" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-voorraad-picking" source="id-diagram-voorraad-check" target="id-diagram-picking-packing" archimateRelationship="id-rel-voorraad-picking"/>
      </child>

      <child xsi:type="archimate:DiagramObject" id="id-diagram-picking-packing" targetConnections="id-conn-voorraad-picking" archimateElement="id-process-picking-packing">
        <bounds x="125" y="470" width="120" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-picking-verzendlabel" source="id-diagram-picking-packing" target="id-diagram-verzendlabel-maken" archimateRelationship="id-rel-picking-verzendlabel"/>
      </child>

      <child xsi:type="archimate:DiagramObject" id="id-diagram-verzendlabel-maken" targetConnections="id-conn-picking-verzendlabel" archimateElement="id-process-verzendlabel-maken">
        <bounds x="125" y="550" width="120" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-verzendlabel-tracking" source="id-diagram-verzendlabel-maken" target="id-diagram-tracking-kopieren" archimateRelationship="id-rel-verzendlabel-tracking"/>
      </child>

      <child xsi:type="archimate:DiagramObject" id="id-diagram-tracking-kopieren" targetConnections="id-conn-verzendlabel-tracking" archimateElement="id-process-tracking-kopieren">
        <bounds x="125" y="630" width="120" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-tracking-communicatie" source="id-diagram-tracking-kopieren" target="id-diagram-klantcommunicatie" archimateRelationship="id-rel-tracking-communicatie"/>
      </child>

      <child xsi:type="archimate:DiagramObject" id="id-diagram-klantcommunicatie" targetConnections="id-conn-tracking-communicatie" archimateElement="id-process-klantcommunicatie">
        <bounds x="125" y="710" width="120" height="55"/>
      </child>

      <!-- Application Systems - Right Side -->
      <child xsi:type="archimate:DiagramObject" id="id-diagram-erp" archimateElement="id-app-erp">
        <bounds x="300" y="260" width="120" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-erp-invoeren" source="id-diagram-erp" target="id-diagram-order-invoeren" archimateRelationship="id-rel-erp-invoeren"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-erp-klant" source="id-diagram-erp" target="id-diagram-klant-controleren" archimateRelationship="id-rel-erp-klant"/>
      </child>

      <child xsi:type="archimate:DiagramObject" id="id-diagram-wms" archimateElement="id-app-wms">
        <bounds x="300" y="430" width="120" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-wms-voorraad" source="id-diagram-wms" target="id-diagram-voorraad-check" archimateRelationship="id-rel-wms-voorraad"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-wms-picking" source="id-diagram-wms" target="id-diagram-picking-packing" archimateRelationship="id-rel-wms-picking"/>
      </child>

      <child xsi:type="archimate:DiagramObject" id="id-diagram-tms" archimateElement="id-app-tms">
        <bounds x="300" y="590" width="120" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-tms-verzendlabel" source="id-diagram-tms" target="id-diagram-verzendlabel-maken" archimateRelationship="id-rel-tms-verzendlabel"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-tms-tracking" source="id-diagram-tracking-kopieren" target="id-diagram-tms" archimateRelationship="id-rel-tms-tracking"/>
      </child>

      <child xsi:type="archimate:DiagramObject" id="id-diagram-crm" archimateElement="id-app-crm">
        <bounds x="300" y="710" width="120" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-crm-communicatie" source="id-diagram-crm" target="id-diagram-klantcommunicatie" archimateRelationship="id-rel-crm-communicatie"/>
      </child>

      <!-- Technology Layer - Far Right -->
      <child xsi:type="archimate:DiagramObject" id="id-diagram-cloud" archimateElement="id-node-cloud">
        <bounds x="480" y="260" width="120" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-erp-cloud" source="id-diagram-erp" target="id-diagram-cloud" archimateRelationship="id-rel-erp-cloud"/>
      </child>

      <child xsi:type="archimate:DiagramObject" id="id-diagram-onpremise" archimateElement="id-node-onpremise">
        <bounds x="480" y="430" width="120" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-wms-onpremise" source="id-diagram-wms" target="id-diagram-onpremise" archimateRelationship="id-rel-wms-onpremise"/>
      </child>

      <child xsi:type="archimate:DiagramObject" id="id-diagram-standalone-tms" archimateElement="id-node-standalone-tms">
        <bounds x="480" y="590" width="120" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-tms-standalone" source="id-diagram-tms" target="id-diagram-standalone-tms" archimateRelationship="id-rel-tms-standalone"/>
      </child>

      <child xsi:type="archimate:DiagramObject" id="id-diagram-standalone-crm" archimateElement="id-node-standalone-crm">
        <bounds x="480" y="710" width="120" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-crm-standalone" source="id-diagram-crm" target="id-diagram-standalone-crm" archimateRelationship="id-rel-crm-standalone"/>
      </child>
    </element>
  </folder>
</archimate:model>
