<?xml version="1.0" encoding="UTF-8"?>
<archimate:model xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:archimate="http://www.archimatetool.com/archimate" name="Logistix BV - E-fulfilment en Distributie" id="id-logistix-final-model" version="5.0.0">
  <folder name="Strategy" id="id-strategy-folder" type="strategy"/>
  <folder name="Business" id="id-business-folder" type="business">
    <element xsi:type="archimate:BusinessActor" name="Grote klant" id="id-actor-grote-klant"/>
    <element xsi:type="archimate:BusinessActor" name="Kleine klant" id="id-actor-kleine-klant"/>
    <element xsi:type="archimate:BusinessActor" name="Logistiek medewerker" id="id-actor-logistiek-medewerker"/>
    
    <element xsi:type="archimate:BusinessRole" name="Orderverwerker" id="id-role-orderverwerker"/>
    
    <element xsi:type="archimate:BusinessService" name="E-fulfilment dienstverlening" id="id-service-efulfilment"/>
    <element xsi:type="archimate:BusinessService" name="Orderverwerking" id="id-service-orderverwerking"/>
    
    <element xsi:type="archimate:BusinessProcess" name="Order ontvangen" id="id-process-order-ontvangen"/>
    <element xsi:type="archimate:BusinessProcess" name="Ordergegevens handmatig invoeren" id="id-process-handmatige-invoer"/>
    <element xsi:type="archimate:BusinessProcess" name="Klantgegevens controleren" id="id-process-klant-controleren"/>
    <element xsi:type="archimate:BusinessProcess" name="Voorraad valideren" id="id-process-voorraad-valideren"/>
    <element xsi:type="archimate:BusinessProcess" name="Picking en packing" id="id-process-picking-packing"/>
    <element xsi:type="archimate:BusinessProcess" name="Verzendlabel aanmaken" id="id-process-verzendlabel"/>
    <element xsi:type="archimate:BusinessProcess" name="Trackingnummer kopiëren" id="id-process-tracking-kopieren"/>
    <element xsi:type="archimate:BusinessProcess" name="Klantcommunicatie" id="id-process-klantcommunicatie"/>
  </folder>
  <folder name="Application" id="id-application-folder" type="application">
    <element xsi:type="archimate:ApplicationService" name="Orderverwerking" id="id-app-service-orderverwerking"/>
    
    <element xsi:type="archimate:ApplicationComponent" name="ERP (cloud)" id="id-app-erp"/>
    <element xsi:type="archimate:ApplicationComponent" name="WMS (on-premise)" id="id-app-wms"/>
    <element xsi:type="archimate:ApplicationComponent" name="TMS (standalone)" id="id-app-tms"/>
    <element xsi:type="archimate:ApplicationComponent" name="CRM (losstaand)" id="id-app-crm"/>
  </folder>
  <folder name="Technology &amp; Physical" id="id-technology-folder" type="technology">
    <element xsi:type="archimate:Node" name="Cloud platform" id="id-node-cloud"/>
    <element xsi:type="archimate:Node" name="On-premise server" id="id-node-onpremise"/>
  </folder>
  <folder name="Motivation" id="id-motivation-folder" type="motivation"/>
  <folder name="Implementation &amp; Migration" id="id-implementation-folder" type="implementation_migration"/>
  <folder name="Other" id="id-other-folder" type="other"/>
  <folder name="Relations" id="id-relations-folder" type="relations">
    <element xsi:type="archimate:TriggeringRelationship" id="id-rel-grote-klant-order" source="id-actor-grote-klant" target="id-process-order-ontvangen"/>
    <element xsi:type="archimate:TriggeringRelationship" id="id-rel-kleine-klant-order" source="id-actor-kleine-klant" target="id-process-order-ontvangen"/>
    <element xsi:type="archimate:AssignmentRelationship" id="id-rel-logistiek-orderverwerker" source="id-actor-logistiek-medewerker" target="id-role-orderverwerker"/>
    <element xsi:type="archimate:ServingRelationship" id="id-rel-orderverwerker-handmatig" source="id-role-orderverwerker" target="id-process-handmatige-invoer"/>
    
    <element xsi:type="archimate:TriggeringRelationship" id="id-rel-order-handmatig" source="id-process-order-ontvangen" target="id-process-handmatige-invoer"/>
    <element xsi:type="archimate:TriggeringRelationship" id="id-rel-handmatig-klant" source="id-process-handmatige-invoer" target="id-process-klant-controleren"/>
    <element xsi:type="archimate:TriggeringRelationship" id="id-rel-handmatig-voorraad" source="id-process-handmatige-invoer" target="id-process-voorraad-valideren"/>
    <element xsi:type="archimate:TriggeringRelationship" id="id-rel-voorraad-picking" source="id-process-voorraad-valideren" target="id-process-picking-packing"/>
    <element xsi:type="archimate:TriggeringRelationship" id="id-rel-picking-verzendlabel" source="id-process-picking-packing" target="id-process-verzendlabel"/>
    <element xsi:type="archimate:TriggeringRelationship" id="id-rel-verzendlabel-tracking" source="id-process-verzendlabel" target="id-process-tracking-kopieren"/>
    
    <element xsi:type="archimate:RealizationRelationship" id="id-rel-erp-klant" source="id-app-erp" target="id-process-klant-controleren"/>
    <element xsi:type="archimate:RealizationRelationship" id="id-rel-wms-voorraad" source="id-app-wms" target="id-process-voorraad-valideren"/>
    <element xsi:type="archimate:RealizationRelationship" id="id-rel-wms-picking" source="id-app-wms" target="id-process-picking-packing"/>
    <element xsi:type="archimate:RealizationRelationship" id="id-rel-tms-verzendlabel" source="id-app-tms" target="id-process-verzendlabel"/>
    <element xsi:type="archimate:RealizationRelationship" id="id-rel-tms-tracking" source="id-app-tms" target="id-process-tracking-kopieren"/>
    <element xsi:type="archimate:RealizationRelationship" id="id-rel-crm-communicatie" source="id-app-crm" target="id-process-klantcommunicatie"/>
    
    <element xsi:type="archimate:RealizationRelationship" id="id-rel-cloud-erp" source="id-node-cloud" target="id-app-erp"/>
    <element xsi:type="archimate:RealizationRelationship" id="id-rel-onpremise-wms" source="id-node-onpremise" target="id-app-wms"/>
    
    <element xsi:type="archimate:RealizationRelationship" id="id-rel-processes-service" source="id-process-order-ontvangen" target="id-service-efulfilment"/>
    <element xsi:type="archimate:ServingRelationship" id="id-rel-apps-service" source="id-app-service-orderverwerking" target="id-service-efulfilment"/>
  </folder>
  <folder name="Views" id="id-views-folder" type="diagrams">
    <element xsi:type="archimate:ArchimateDiagramModel" name="Logistix BV - Huidige Situatie" id="id-view-current-state">
      <!-- Business Actors -->
      <child xsi:type="archimate:DiagramObject" id="id-diagram-grote-klant" archimateElement="id-actor-grote-klant">
        <bounds x="50" y="50" width="120" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-grote-klant-order" source="id-diagram-grote-klant" target="id-diagram-order-ontvangen" archimateRelationship="id-rel-grote-klant-order"/>
      </child>
      
      <child xsi:type="archimate:DiagramObject" id="id-diagram-kleine-klant" archimateElement="id-actor-kleine-klant">
        <bounds x="200" y="50" width="120" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-kleine-klant-order" source="id-diagram-kleine-klant" target="id-diagram-order-ontvangen" archimateRelationship="id-rel-kleine-klant-order"/>
      </child>
      
      <child xsi:type="archimate:DiagramObject" id="id-diagram-logistiek-medewerker" archimateElement="id-actor-logistiek-medewerker">
        <bounds x="400" y="150" width="120" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-logistiek-orderverwerker" source="id-diagram-logistiek-medewerker" target="id-diagram-orderverwerker" archimateRelationship="id-rel-logistiek-orderverwerker"/>
      </child>
      
      <!-- Business Role -->
      <child xsi:type="archimate:DiagramObject" id="id-diagram-orderverwerker" targetConnections="id-conn-logistiek-orderverwerker" archimateElement="id-role-orderverwerker">
        <bounds x="350" y="250" width="120" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-orderverwerker-handmatig" source="id-diagram-orderverwerker" target="id-diagram-handmatige-invoer" archimateRelationship="id-rel-orderverwerker-handmatig"/>
      </child>
      
      <!-- Klantcommunicatie (separate) -->
      <child xsi:type="archimate:DiagramObject" id="id-diagram-klantcommunicatie" archimateElement="id-process-klantcommunicatie">
        <bounds x="600" y="250" width="120" height="55"/>
      </child>
      
      <!-- Main Business Service Container -->
      <child xsi:type="archimate:DiagramObject" id="id-diagram-service-container" archimateElement="id-service-efulfilment">
        <bounds x="50" y="350" width="700" height="150"/>
        
        <!-- Order ontvangen -->
        <child xsi:type="archimate:DiagramObject" id="id-diagram-order-ontvangen" targetConnections="id-conn-grote-klant-order id-conn-kleine-klant-order" archimateElement="id-process-order-ontvangen">
          <bounds x="20" y="20" width="120" height="55"/>
          <sourceConnection xsi:type="archimate:Connection" id="id-conn-order-handmatig" source="id-diagram-order-ontvangen" target="id-diagram-handmatige-invoer" archimateRelationship="id-rel-order-handmatig"/>
        </child>
        
        <!-- Handmatige invoer -->
        <child xsi:type="archimate:DiagramObject" id="id-diagram-handmatige-invoer" targetConnections="id-conn-order-handmatig id-conn-orderverwerker-handmatig" archimateElement="id-process-handmatige-invoer">
          <bounds x="170" y="20" width="120" height="55"/>
          <sourceConnection xsi:type="archimate:Connection" id="id-conn-handmatig-klant" source="id-diagram-handmatige-invoer" target="id-diagram-klant-controleren" archimateRelationship="id-rel-handmatig-klant"/>
          <sourceConnection xsi:type="archimate:Connection" id="id-conn-handmatig-voorraad" source="id-diagram-handmatige-invoer" target="id-diagram-voorraad-valideren" archimateRelationship="id-rel-handmatig-voorraad"/>
        </child>
        
        <!-- Klant controleren -->
        <child xsi:type="archimate:DiagramObject" id="id-diagram-klant-controleren" targetConnections="id-conn-handmatig-klant" archimateElement="id-process-klant-controleren">
          <bounds x="320" y="20" width="120" height="55"/>
        </child>
        
        <!-- Trackingnummer kopiëren -->
        <child xsi:type="archimate:DiagramObject" id="id-diagram-tracking-kopieren" archimateElement="id-process-tracking-kopieren">
          <bounds x="550" y="20" width="120" height="55"/>
        </child>
        
        <!-- Voorraad valideren -->
        <child xsi:type="archimate:DiagramObject" id="id-diagram-voorraad-valideren" targetConnections="id-conn-handmatig-voorraad" archimateElement="id-process-voorraad-valideren">
          <bounds x="170" y="80" width="120" height="55"/>
          <sourceConnection xsi:type="archimate:Connection" id="id-conn-voorraad-picking" source="id-diagram-voorraad-valideren" target="id-diagram-picking-packing" archimateRelationship="id-rel-voorraad-picking"/>
        </child>
        
        <!-- Picking en packing -->
        <child xsi:type="archimate:DiagramObject" id="id-diagram-picking-packing" targetConnections="id-conn-voorraad-picking" archimateElement="id-process-picking-packing">
          <bounds x="320" y="80" width="120" height="55"/>
          <sourceConnection xsi:type="archimate:Connection" id="id-conn-picking-verzendlabel" source="id-diagram-picking-packing" target="id-diagram-verzendlabel" archimateRelationship="id-rel-picking-verzendlabel"/>
        </child>
        
        <!-- Verzendlabel aanmaken -->
        <child xsi:type="archimate:DiagramObject" id="id-diagram-verzendlabel" targetConnections="id-conn-picking-verzendlabel" archimateElement="id-process-verzendlabel">
          <bounds x="470" y="80" width="120" height="55"/>
          <sourceConnection xsi:type="archimate:Connection" id="id-conn-verzendlabel-tracking" source="id-diagram-verzendlabel" target="id-diagram-tracking-kopieren" archimateRelationship="id-rel-verzendlabel-tracking"/>
        </child>
      </child>
      
      <!-- Application Service Container -->
      <child xsi:type="archimate:DiagramObject" id="id-diagram-app-service-container" archimateElement="id-app-service-orderverwerking">
        <bounds x="50" y="550" width="700" height="120"/>
        
        <!-- ERP System -->
        <child xsi:type="archimate:DiagramObject" id="id-diagram-erp" archimateElement="id-app-erp">
          <bounds x="20" y="20" width="150" height="75"/>
          <sourceConnection xsi:type="archimate:Connection" id="id-conn-erp-klant" source="id-diagram-erp" target="id-diagram-klant-controleren" archimateRelationship="id-rel-erp-klant"/>
        </child>
        
        <!-- WMS System -->
        <child xsi:type="archimate:DiagramObject" id="id-diagram-wms" archimateElement="id-app-wms">
          <bounds x="190" y="20" width="150" height="75"/>
          <sourceConnection xsi:type="archimate:Connection" id="id-conn-wms-voorraad" source="id-diagram-wms" target="id-diagram-voorraad-valideren" archimateRelationship="id-rel-wms-voorraad"/>
          <sourceConnection xsi:type="archimate:Connection" id="id-conn-wms-picking" source="id-diagram-wms" target="id-diagram-picking-packing" archimateRelationship="id-rel-wms-picking"/>
        </child>
        
        <!-- TMS System -->
        <child xsi:type="archimate:DiagramObject" id="id-diagram-tms" archimateElement="id-app-tms">
          <bounds x="360" y="20" width="150" height="75"/>
          <sourceConnection xsi:type="archimate:Connection" id="id-conn-tms-verzendlabel" source="id-diagram-tms" target="id-diagram-verzendlabel" archimateRelationship="id-rel-tms-verzendlabel"/>
          <sourceConnection xsi:type="archimate:Connection" id="id-conn-tms-tracking" source="id-diagram-tms" target="id-diagram-tracking-kopieren" archimateRelationship="id-rel-tms-tracking"/>
        </child>
        
        <!-- CRM System -->
        <child xsi:type="archimate:DiagramObject" id="id-diagram-crm" archimateElement="id-app-crm">
          <bounds x="530" y="20" width="150" height="75"/>
          <sourceConnection xsi:type="archimate:Connection" id="id-conn-crm-communicatie" source="id-diagram-crm" target="id-diagram-klantcommunicatie" archimateRelationship="id-rel-crm-communicatie"/>
        </child>
      </child>
      
      <!-- Technology Layer -->
      <child xsi:type="archimate:DiagramObject" id="id-diagram-cloud" archimateElement="id-node-cloud">
        <bounds x="70" y="700" width="150" height="60"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-cloud-erp" source="id-diagram-cloud" target="id-diagram-erp" archimateRelationship="id-rel-cloud-erp"/>
      </child>
      
      <child xsi:type="archimate:DiagramObject" id="id-diagram-onpremise" archimateElement="id-node-onpremise">
        <bounds x="240" y="700" width="150" height="60"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-onpremise-wms" source="id-diagram-onpremise" target="id-diagram-wms" archimateRelationship="id-rel-onpremise-wms"/>
      </child>
    </element>
  </folder>
</archimate:model>
