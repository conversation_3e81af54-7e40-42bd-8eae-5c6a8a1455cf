<?xml version="1.0" encoding="UTF-8"?>
<archimate:model xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:archimate="http://www.archimatetool.com/archimate" name="Logistix BV - Logistieke Dienstverlening" id="id-logistix-model-2024" version="5.0.0">
  <folder name="Strategy" id="id-strategy-folder-001" type="strategy"/>
  <folder name="Business" id="id-business-folder-001" type="business">
    <element xsi:type="archimate:BusinessActor" name="Klant" id="id-actor-klant-001"/>
    <element xsi:type="archimate:BusinessActor" name="Logistiek medewerker" id="id-actor-logistiek-001"/>
    <element xsi:type="archimate:BusinessService" name="Logistieke dienstverlening" id="id-service-logistiek-001"/>
    <element xsi:type="archimate:BusinessProcess" name="Order verwerken" id="id-process-order-001"/>
    <element xsi:type="archimate:BusinessRole" name="Orderverwerker" id="id-role-orderverwerker-001"/>
    <element xsi:type="archimate:BusinessProcess" name="Voorraad controleren" id="id-process-voorraad-001"/>
    <element xsi:type="archimate:BusinessProcess" name="Goederen verzenden" id="id-process-verzenden-001"/>
    <element xsi:type="archimate:BusinessObject" name="Order" id="id-object-order-001"/>
  </folder>
  <folder name="Application" id="id-application-folder-001" type="application">
    <element xsi:type="archimate:ApplicationService" name="Order Management" id="id-app-service-order-001"/>
    <element xsi:type="archimate:ApplicationComponent" name="ERP Systeem" id="id-app-erp-001"/>
    <element xsi:type="archimate:ApplicationComponent" name="WMS Systeem" id="id-app-wms-001"/>
    <element xsi:type="archimate:DataObject" name="Ordergegevens" id="id-data-order-001"/>
  </folder>
  <folder name="Technology &amp; Physical" id="id-technology-folder-001" type="technology">
    <element xsi:type="archimate:Node" name="Application Server" id="id-node-appserver-001"/>
    <element xsi:type="archimate:Device" name="Barcode Scanner" id="id-device-scanner-001"/>
    <element xsi:type="archimate:CommunicationNetwork" name="Netwerk (LAN)" id="id-network-lan-001"/>
  </folder>
  <folder name="Motivation" id="id-motivation-folder-001" type="motivation"/>
  <folder name="Implementation &amp; Migration" id="id-implementation-folder-001" type="implementation_migration"/>
  <folder name="Other" id="id-other-folder-001" type="other"/>
  <folder name="Relations" id="id-relations-folder-001" type="relations">
    <element xsi:type="archimate:TriggeringRelationship" id="id-rel-trigger-001" source="id-actor-klant-001" target="id-process-order-001"/>
    <element xsi:type="archimate:ServingRelationship" id="id-rel-serving-001" source="id-process-order-001" target="id-actor-logistiek-001"/>
    <element xsi:type="archimate:ServingRelationship" id="id-rel-serving-002" source="id-role-orderverwerker-001" target="id-process-order-001"/>
    <element xsi:type="archimate:AssociationRelationship" id="id-rel-association-001" source="id-role-orderverwerker-001" target="id-actor-logistiek-001"/>
    <element xsi:type="archimate:CompositionRelationship" id="id-rel-composition-001" source="id-process-order-001" target="id-process-voorraad-001"/>
    <element xsi:type="archimate:CompositionRelationship" id="id-rel-composition-002" source="id-process-order-001" target="id-process-verzenden-001"/>
    <element xsi:type="archimate:RealizationRelationship" id="id-rel-realization-001" source="id-node-appserver-001" target="id-app-erp-001"/>
    <element xsi:type="archimate:RealizationRelationship" id="id-rel-realization-002" source="id-app-erp-001" target="id-process-order-001"/>
    <element xsi:type="archimate:AccessRelationship" id="id-rel-access-001" source="id-service-logistiek-001" target="id-object-order-001"/>
    <element xsi:type="archimate:AccessRelationship" id="id-rel-access-002" source="id-process-order-001" target="id-object-order-001"/>
    <element xsi:type="archimate:RealizationRelationship" id="id-rel-realization-003" source="id-process-order-001" target="id-service-logistiek-001"/>
    <element xsi:type="archimate:AssociationRelationship" id="id-rel-association-002" source="id-network-lan-001" target="id-device-scanner-001"/>
    <element xsi:type="archimate:AssociationRelationship" id="id-rel-association-003" source="id-node-appserver-001" target="id-network-lan-001"/>
    <element xsi:type="archimate:AccessRelationship" id="id-rel-access-003" source="id-app-erp-001" target="id-data-order-001"/>
  </folder>
  <folder name="Views" id="id-views-folder-001" type="diagrams">
    <element xsi:type="archimate:ArchimateDiagramModel" name="Logistix BV - Hoofdoverzicht" id="id-view-main-001">
      <child xsi:type="archimate:DiagramObject" id="id-diagram-logistiek-001" targetConnections="id-conn-serving-001" archimateElement="id-actor-logistiek-001">
        <bounds x="300" y="276" width="120" height="55"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="id-diagram-klant-001" archimateElement="id-actor-klant-001">
        <bounds x="144" y="276" width="120" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-trigger-001" source="id-diagram-klant-001" target="id-diagram-process-order-001" archimateRelationship="id-rel-trigger-001"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="id-diagram-app-service-001" archimateElement="id-app-service-order-001">
        <bounds x="36" y="564" width="400" height="100"/>
        <child xsi:type="archimate:DiagramObject" id="id-diagram-erp-001" archimateElement="id-app-erp-001">
          <bounds x="50" y="30" width="120" height="55"/>
          <sourceConnection xsi:type="archimate:Connection" id="id-conn-realization-002" source="id-diagram-erp-001" target="id-diagram-process-order-001" archimateRelationship="id-rel-realization-002"/>
        </child>
        <child xsi:type="archimate:DiagramObject" id="id-diagram-wms-001" archimateElement="id-app-wms-001">
          <bounds x="200" y="30" width="120" height="55"/>
        </child>
      </child>
      <child xsi:type="archimate:DiagramObject" id="id-diagram-appserver-001" archimateElement="id-node-appserver-001">
        <bounds x="168" y="750" width="200" height="49"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-realization-001" source="id-diagram-appserver-001" target="id-diagram-erp-001" archimateRelationship="id-rel-realization-001"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-association-003" source="id-diagram-appserver-001" target="id-diagram-network-001" archimateRelationship="id-rel-association-003"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="id-diagram-process-order-001" targetConnections="id-conn-trigger-001 id-conn-realization-002 id-conn-serving-002" archimateElement="id-process-order-001">
        <bounds x="168" y="456" width="300" height="85"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-serving-001" source="id-diagram-process-order-001" target="id-diagram-logistiek-001" archimateRelationship="id-rel-serving-001"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-composition-001" source="id-diagram-process-order-001" target="id-diagram-process-voorraad-001" archimateRelationship="id-rel-composition-001"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-composition-002" source="id-diagram-process-order-001" target="id-diagram-process-verzenden-001" archimateRelationship="id-rel-composition-002"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-realization-003" source="id-diagram-process-order-001" target="id-diagram-service-001" archimateRelationship="id-rel-realization-003"/>
        <child xsi:type="archimate:DiagramObject" id="id-diagram-process-voorraad-001" targetConnections="id-conn-composition-001" archimateElement="id-process-voorraad-001">
          <bounds x="24" y="24" width="120" height="55"/>
        </child>
        <child xsi:type="archimate:DiagramObject" id="id-diagram-process-verzenden-001" targetConnections="id-conn-composition-002" archimateElement="id-process-verzenden-001">
          <bounds x="156" y="24" width="120" height="55"/>
        </child>
      </child>
      <child xsi:type="archimate:DiagramObject" id="id-diagram-role-001" archimateElement="id-role-orderverwerker-001">
        <bounds x="311" y="360" width="133" height="44"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-association-001" source="id-diagram-role-001" target="id-diagram-logistiek-001" archimateRelationship="id-rel-association-001"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-serving-002" source="id-diagram-role-001" target="id-diagram-process-order-001" archimateRelationship="id-rel-serving-002"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="id-diagram-service-001" targetConnections="id-conn-realization-003" archimateElement="id-service-logistiek-001">
        <bounds x="415" y="192" width="168" height="55"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="id-diagram-network-001" targetConnections="id-conn-association-003" archimateElement="id-network-lan-001">
        <bounds x="156" y="850" width="300" height="48"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-association-002" source="id-diagram-network-001" target="id-diagram-scanner-001" archimateRelationship="id-rel-association-002"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="id-diagram-scanner-001" targetConnections="id-conn-association-002" archimateElement="id-device-scanner-001">
        <bounds x="500" y="850" width="120" height="49"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="id-diagram-order-data-001" archimateElement="id-data-order-001">
        <bounds x="50" y="700" width="120" height="40"/>
      </child>
    </element>
  </folder>
</archimate:model>
