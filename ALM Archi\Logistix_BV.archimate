<?xml version="1.0" encoding="UTF-8"?>
<archimate:model xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:archimate="http://www.archimatetool.com/archimate" name="Logistix BV - Enterprise Architecture" id="id-a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6" version="5.0.0">
  <folder name="Strategy" id="id-s1t2r3a4t5e6g7y8" type="strategy"/>
  <folder name="Business" id="id-b1u2s3i4n5e6s7s8" type="business">
    <element xsi:type="archimate:BusinessActor" name="Klant" id="id-a1c2t3o4r5c6u7s8t9o0m1e2r3"/>
    <element xsi:type="archimate:BusinessActor" name="Logistiek medewerker" id="id-a1c2t3o4r5l6o7g8i9s0t1i2c3s4"/>
    <element xsi:type="archimate:BusinessActor" name="Magazijnmedewerker" id="id-a1c2t3o4r5w6a7r8e9h0o1u2s3e4"/>
    <element xsi:type="archimate:BusinessRole" name="Orderverwerker" id="id-r1o2l3e4o5r6d7e8r9p0r1o2c3e4s5s6o7r8"/>
    <element xsi:type="archimate:BusinessService" name="Logistieke dienstverlening" id="id-s1e2r3v4i5c6e7l8o9g0i1s2t3i4c5s6"/>
    <element xsi:type="archimate:BusinessProcess" name="Order ontvangen" id="id-p1r2o3c4e5s6s7r8e9c0e1i2v3e4o5r6d7e8r9"/>
    <element xsi:type="archimate:BusinessProcess" name="Order verwerken" id="id-p1r2o3c4e5s6s7p8r9o0c1e2s3s4o5r6d7e8r9"/>
    <element xsi:type="archimate:BusinessProcess" name="Voorraad controleren" id="id-p1r2o3c4e5s6s7c8h9e0c1k2i3n4v5e6n7t8o9r0y1"/>
    <element xsi:type="archimate:BusinessProcess" name="Goederen verzenden" id="id-p1r2o3c4e5s6s7s8h9i0p1g2o3o4d5s6"/>
    <element xsi:type="archimate:BusinessObject" name="Order" id="id-o1b2j3e4c5t6o7r8d9e0r1"/>
    <element xsi:type="archimate:BusinessObject" name="Voorraaditem" id="id-o1b2j3e4c5t6i7n8v9e0n1t2o3r4y5i6t7e8m9"/>
  </folder>
  <folder name="Application" id="id-a1p2p3l4i5c6a7t8i9o0n1" type="application">
    <element xsi:type="archimate:ApplicationService" name="Order Management" id="id-a1p2p3s4e5r6v7i8c9e0o1r2d3e4r5m6g7m8t9"/>
    <element xsi:type="archimate:ApplicationComponent" name="ERP Systeem" id="id-a1p2p3c4o5m6p7e8r9p0s1y2s3t4e5e6m7"/>
    <element xsi:type="archimate:ApplicationComponent" name="WMS Systeem" id="id-a1p2p3c4o5m6p7w8m9s0s1y2s3t4e5e6m7"/>
    <element xsi:type="archimate:DataObject" name="Ordergegevens" id="id-d1a2t3a4o5r6d7e8r9d0a1t2a3"/>
    <element xsi:type="archimate:DataObject" name="Voorraadgegevens" id="id-d1a2t3a4i5n6v7e8n9t0o1r2y3d4a5t6a7"/>
  </folder>
  <folder name="Technology &amp; Physical" id="id-t1e2c3h4n5o6l7o8g9y0" type="technology">
    <element xsi:type="archimate:Node" name="Application Server" id="id-n1o2d3e4a5p6p7s8e9r0v1e2r3"/>
    <element xsi:type="archimate:Device" name="Barcode Scanner" id="id-d1e2v3i4c5e6b7a8r9c0o1d2e3s4c5a6n7n8e9r0"/>
    <element xsi:type="archimate:CommunicationNetwork" name="Netwerk" id="id-n1e2t3w4o5r6k7"/>
  </folder>
  <folder name="Motivation" id="id-m1o2t3i4v5a6t7i8o9n0" type="motivation"/>
  <folder name="Implementation &amp; Migration" id="id-i1m2p3l4e5m6e7n8t9a0t1i2o3n4" type="implementation_migration"/>
  <folder name="Other" id="id-o1t2h3e4r5" type="other"/>
  <folder name="Relations" id="id-r1e2l3a4t5i6o7n8s9" type="relations">
    <element xsi:type="archimate:TriggeringRelationship" id="id-r1e2l3t4r5i6g7g8e9r0" source="id-a1c2t3o4r5c6u7s8t9o0m1e2r3" target="id-p1r2o3c4e5s6s7r8e9c0e1i2v3e4o5r6d7e8r9"/>
    <element xsi:type="archimate:TriggeringRelationship" id="id-r1e2l3t4r5i6g7g8e9r1" source="id-p1r2o3c4e5s6s7r8e9c0e1i2v3e4o5r6d7e8r9" target="id-p1r2o3c4e5s6s7p8r9o0c1e2s3s4o5r6d7e8r9"/>
    <element xsi:type="archimate:TriggeringRelationship" id="id-r1e2l3t4r5i6g7g8e9r2" source="id-p1r2o3c4e5s6s7p8r9o0c1e2s3s4o5r6d7e8r9" target="id-p1r2o3c4e5s6s7c8h9e0c1k2i3n4v5e6n7t8o9r0y1"/>
    <element xsi:type="archimate:ServingRelationship" id="id-r1e2l3s4e5r6v7i8n9g0" source="id-a1p2p3c4o5m6p7e8r9p0s1y2s3t4e5e6m7" target="id-a1p2p3s4e5r6v7i8c9e0o1r2d3e4r5m6g7m8t9"/>
    <element xsi:type="archimate:RealizationRelationship" id="id-r1e2l3r4e5a6l7i8z9a0t1i2o3n4" source="id-a1p2p3s4e5r6v7i8c9e0o1r2d3e4r5m6g7m8t9" target="id-p1r2o3c4e5s6s7p8r9o0c1e2s3s4o5r6d7e8r9"/>
    <element xsi:type="archimate:AccessRelationship" id="id-r1e2l3a4c5c6e7s8s9" source="id-a1p2p3c4o5m6p7e8r9p0s1y2s3t4e5e6m7" target="id-d1a2t3a4o5r6d7e8r9d0a1t2a3"/>
  </folder>
  <folder name="Views" id="id-v1i2e3w4s5" type="diagrams">
    <element xsi:type="archimate:ArchimateDiagramModel" name="Logistix BV - Overzicht" id="id-v1i2e3w4m5a6i7n8">
      <child xsi:type="archimate:DiagramObject" id="id-d1i2a3g4r5a6m7c8u9s0t1o2m3e4r5" archimateElement="id-a1c2t3o4r5c6u7s8t9o0m1e2r3">
        <bounds x="50" y="50" width="120" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-c1o2n3n4e5c6t7i8o9n0" source="id-d1i2a3g4r5a6m7c8u9s0t1o2m3e4r5" target="id-d1i2a3g4r5a6m7o8r9d0e1r2" archimateRelationship="id-r1e2l3t4r5i6g7g8e9r0"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="id-d1i2a3g4r5a6m7o8r9d0e1r2" targetConnections="id-c1o2n3n4e5c6t7i8o9n0" archimateElement="id-p1r2o3c4e5s6s7r8e9c0e1i2v3e4o5r6d7e8r9">
        <bounds x="200" y="50" width="120" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-c1o2n3n4e5c6t7i8o9n1" source="id-d1i2a3g4r5a6m7o8r9d0e1r2" target="id-d1i2a3g4r5a6m7p8r9o0c1e2s3s4" archimateRelationship="id-r1e2l3t4r5i6g7g8e9r1"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="id-d1i2a3g4r5a6m7p8r9o0c1e2s3s4" targetConnections="id-c1o2n3n4e5c6t7i8o9n1" archimateElement="id-p1r2o3c4e5s6s7p8r9o0c1e2s3s4o5r6d7e8r9">
        <bounds x="350" y="50" width="120" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-c1o2n3n4e5c6t7i8o9n2" source="id-d1i2a3g4r5a6m7p8r9o0c1e2s3s4" target="id-d1i2a3g4r5a6m7c8h9e0c1k2" archimateRelationship="id-r1e2l3t4r5i6g7g8e9r2"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="id-d1i2a3g4r5a6m7c8h9e0c1k2" targetConnections="id-c1o2n3n4e5c6t7i8o9n2" archimateElement="id-p1r2o3c4e5s6s7c8h9e0c1k2i3n4v5e6n7t8o9r0y1">
        <bounds x="500" y="50" width="120" height="55"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="id-d1i2a3g4r5a6m7e8r9p0" archimateElement="id-a1p2p3c4o5m6p7e8r9p0s1y2s3t4e5e6m7">
        <bounds x="100" y="200" width="150" height="75"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-c1o2n3n4e5c6t7i8o9n3" source="id-d1i2a3g4r5a6m7e8r9p0" target="id-d1i2a3g4r5a6m7s8e9r0v1i2c3e4" archimateRelationship="id-r1e2l3s4e5r6v7i8n9g0"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="id-d1i2a3g4r5a6m7s8e9r0v1i2c3e4" targetConnections="id-c1o2n3n4e5c6t7i8o9n3" archimateElement="id-a1p2p3s4e5r6v7i8c9e0o1r2d3e4r5m6g7m8t9">
        <bounds x="300" y="200" width="120" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-c1o2n3n4e5c6t7i8o9n4" source="id-d1i2a3g4r5a6m7s8e9r0v1i2c3e4" target="id-d1i2a3g4r5a6m7p8r9o0c1e2s3s4" archimateRelationship="id-r1e2l3r4e5a6l7i8z9a0t1i2o3n4"/>
      </child>
    </element>
  </folder>
</archimate:model>
