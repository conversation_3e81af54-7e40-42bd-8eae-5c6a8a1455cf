<?xml version="1.0" encoding="UTF-8"?>
<archimate:model xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:archimate="http://www.archimatetool.com/archimate" name="Logistix BV - Enterprise Architecture" id="id-logistix-main-model" version="5.0.0">
  <folder name="Strategy" id="id-strategy-folder" type="strategy">
    <element xsi:type="archimate:Goal" name="Efficiënte logistieke dienstverlening" id="id-goal-efficient-logistics"/>
    <element xsi:type="archimate:Outcome" name="Verhoogde klanttevredenheid" id="id-outcome-customer-satisfaction"/>
    <element xsi:type="archimate:Principle" name="Digitalisering van processen" id="id-principle-digitalization"/>
  </folder>
  <folder name="Business" id="id-business-folder" type="business">
    <element xsi:type="archimate:BusinessActor" name="Klant" id="id-actor-customer"/>
    <element xsi:type="archimate:BusinessActor" name="Logistiek medewerker" id="id-actor-logistics-employee"/>
    <element xsi:type="archimate:BusinessActor" name="Ma<PERSON><PERSON>jnmedewerker" id="id-actor-warehouse-employee"/>
    <element xsi:type="archimate:BusinessActor" name="Transporteur" id="id-actor-transporter"/>
    <element xsi:type="archimate:BusinessActor" name="Leverancier" id="id-actor-supplier"/>
    
    <element xsi:type="archimate:BusinessRole" name="Orderverwerker" id="id-role-order-processor"/>
    <element xsi:type="archimate:BusinessRole" name="Magazijnbeheerder" id="id-role-warehouse-manager"/>
    <element xsi:type="archimate:BusinessRole" name="Transportplanner" id="id-role-transport-planner"/>
    
    <element xsi:type="archimate:BusinessService" name="Logistieke dienstverlening" id="id-service-logistics"/>
    <element xsi:type="archimate:BusinessService" name="Orderverwerking" id="id-service-order-processing"/>
    <element xsi:type="archimate:BusinessService" name="Magazijnbeheer" id="id-service-warehouse-management"/>
    <element xsi:type="archimate:BusinessService" name="Transport en distributie" id="id-service-transport"/>
    
    <element xsi:type="archimate:BusinessProcess" name="Order ontvangen" id="id-process-receive-order"/>
    <element xsi:type="archimate:BusinessProcess" name="Order verwerken" id="id-process-process-order"/>
    <element xsi:type="archimate:BusinessProcess" name="Voorraad controleren" id="id-process-check-inventory"/>
    <element xsi:type="archimate:BusinessProcess" name="Goederen picken" id="id-process-pick-goods"/>
    <element xsi:type="archimate:BusinessProcess" name="Goederen verpakken" id="id-process-pack-goods"/>
    <element xsi:type="archimate:BusinessProcess" name="Transport plannen" id="id-process-plan-transport"/>
    <element xsi:type="archimate:BusinessProcess" name="Goederen verzenden" id="id-process-ship-goods"/>
    <element xsi:type="archimate:BusinessProcess" name="Levering tracken" id="id-process-track-delivery"/>
    
    <element xsi:type="archimate:BusinessObject" name="Order" id="id-object-order"/>
    <element xsi:type="archimate:BusinessObject" name="Voorraaditem" id="id-object-inventory-item"/>
    <element xsi:type="archimate:BusinessObject" name="Zending" id="id-object-shipment"/>
    <element xsi:type="archimate:BusinessObject" name="Factuur" id="id-object-invoice"/>
  </folder>
  <folder name="Application" id="id-application-folder" type="application">
    <element xsi:type="archimate:ApplicationService" name="Order Management" id="id-app-service-order-mgmt"/>
    <element xsi:type="archimate:ApplicationService" name="Warehouse Management" id="id-app-service-warehouse-mgmt"/>
    <element xsi:type="archimate:ApplicationService" name="Transport Management" id="id-app-service-transport-mgmt"/>
    <element xsi:type="archimate:ApplicationService" name="Track & Trace" id="id-app-service-track-trace"/>
    
    <element xsi:type="archimate:ApplicationComponent" name="ERP Systeem" id="id-app-erp-system"/>
    <element xsi:type="archimate:ApplicationComponent" name="WMS (Warehouse Management System)" id="id-app-wms"/>
    <element xsi:type="archimate:ApplicationComponent" name="TMS (Transport Management System)" id="id-app-tms"/>
    <element xsi:type="archimate:ApplicationComponent" name="CRM Systeem" id="id-app-crm"/>
    <element xsi:type="archimate:ApplicationComponent" name="EDI Gateway" id="id-app-edi-gateway"/>
    
    <element xsi:type="archimate:DataObject" name="Ordergegevens" id="id-data-order-data"/>
    <element xsi:type="archimate:DataObject" name="Voorraadgegevens" id="id-data-inventory-data"/>
    <element xsi:type="archimate:DataObject" name="Klantgegevens" id="id-data-customer-data"/>
    <element xsi:type="archimate:DataObject" name="Transportgegevens" id="id-data-transport-data"/>
  </folder>
  <folder name="Technology &amp; Physical" id="id-technology-folder" type="technology">
    <element xsi:type="archimate:TechnologyService" name="Database Services" id="id-tech-service-database"/>
    <element xsi:type="archimate:TechnologyService" name="Integration Services" id="id-tech-service-integration"/>
    <element xsi:type="archimate:TechnologyService" name="Web Services" id="id-tech-service-web"/>
    
    <element xsi:type="archimate:Node" name="Application Server" id="id-node-app-server"/>
    <element xsi:type="archimate:Node" name="Database Server" id="id-node-db-server"/>
    <element xsi:type="archimate:Node" name="Web Server" id="id-node-web-server"/>
    
    <element xsi:type="archimate:Device" name="Barcode Scanner" id="id-device-barcode-scanner"/>
    <element xsi:type="archimate:Device" name="Handheld Terminal" id="id-device-handheld"/>
    <element xsi:type="archimate:Device" name="Printer" id="id-device-printer"/>
    
    <element xsi:type="archimate:CommunicationNetwork" name="LAN" id="id-network-lan"/>
    <element xsi:type="archimate:CommunicationNetwork" name="Internet" id="id-network-internet"/>
    
    <element xsi:type="archimate:Facility" name="Magazijn" id="id-facility-warehouse"/>
    <element xsi:type="archimate:Facility" name="Kantoor" id="id-facility-office"/>
  </folder>
  <folder name="Motivation" id="id-motivation-folder" type="motivation">
    <element xsi:type="archimate:Stakeholder" name="Management" id="id-stakeholder-management"/>
    <element xsi:type="archimate:Stakeholder" name="Klanten" id="id-stakeholder-customers"/>
    <element xsi:type="archimate:Stakeholder" name="Medewerkers" id="id-stakeholder-employees"/>
    
    <element xsi:type="archimate:Driver" name="Kostenbesparing" id="id-driver-cost-reduction"/>
    <element xsi:type="archimate:Driver" name="Klanttevredenheid" id="id-driver-customer-satisfaction"/>
    <element xsi:type="archimate:Driver" name="Operationele efficiëntie" id="id-driver-operational-efficiency"/>
    
    <element xsi:type="archimate:Assessment" name="Huidige processen zijn handmatig" id="id-assessment-manual-processes"/>
    <element xsi:type="archimate:Assessment" name="Gebrek aan real-time inzicht" id="id-assessment-lack-realtime"/>
  </folder>
  <folder name="Implementation &amp; Migration" id="id-implementation-folder" type="implementation_migration">
    <element xsi:type="archimate:WorkPackage" name="ERP Implementatie" id="id-workpackage-erp-impl"/>
    <element xsi:type="archimate:WorkPackage" name="WMS Implementatie" id="id-workpackage-wms-impl"/>
    <element xsi:type="archimate:WorkPackage" name="Integratie ontwikkeling" id="id-workpackage-integration"/>
    
    <element xsi:type="archimate:Deliverable" name="Geïmplementeerd ERP systeem" id="id-deliverable-erp"/>
    <element xsi:type="archimate:Deliverable" name="Geïmplementeerd WMS systeem" id="id-deliverable-wms"/>
  </folder>
  <folder name="Other" id="id-other-folder" type="other"/>
  <folder name="Relations" id="id-relations-folder" type="relations">
    <!-- Business Relations -->
    <element xsi:type="archimate:TriggeringRelationship" id="id-rel-customer-triggers-order" source="id-actor-customer" target="id-process-receive-order"/>
    <element xsi:type="archimate:AssignmentRelationship" id="id-rel-employee-assigned-processor" source="id-actor-logistics-employee" target="id-role-order-processor"/>
    <element xsi:type="archimate:AssignmentRelationship" id="id-rel-warehouse-employee-assigned-manager" source="id-actor-warehouse-employee" target="id-role-warehouse-manager"/>
    
    <!-- Process Flow Relations -->
    <element xsi:type="archimate:TriggeringRelationship" id="id-rel-receive-triggers-process" source="id-process-receive-order" target="id-process-process-order"/>
    <element xsi:type="archimate:TriggeringRelationship" id="id-rel-process-triggers-check" source="id-process-process-order" target="id-process-check-inventory"/>
    <element xsi:type="archimate:TriggeringRelationship" id="id-rel-check-triggers-pick" source="id-process-check-inventory" target="id-process-pick-goods"/>
    <element xsi:type="archimate:TriggeringRelationship" id="id-rel-pick-triggers-pack" source="id-process-pick-goods" target="id-process-pack-goods"/>
    <element xsi:type="archimate:TriggeringRelationship" id="id-rel-pack-triggers-plan" source="id-process-pack-goods" target="id-process-plan-transport"/>
    <element xsi:type="archimate:TriggeringRelationship" id="id-rel-plan-triggers-ship" source="id-process-plan-transport" target="id-process-ship-goods"/>
    <element xsi:type="archimate:TriggeringRelationship" id="id-rel-ship-triggers-track" source="id-process-ship-goods" target="id-process-track-delivery"/>
    
    <!-- Service Realization Relations -->
    <element xsi:type="archimate:RealizationRelationship" id="id-rel-order-process-realizes-service" source="id-process-process-order" target="id-service-order-processing"/>
    <element xsi:type="archimate:RealizationRelationship" id="id-rel-warehouse-processes-realize-service" source="id-process-check-inventory" target="id-service-warehouse-management"/>
    <element xsi:type="archimate:RealizationRelationship" id="id-rel-transport-processes-realize-service" source="id-process-plan-transport" target="id-service-transport"/>
    
    <!-- Application Support Relations -->
    <element xsi:type="archimate:ServingRelationship" id="id-rel-erp-serves-order-mgmt" source="id-app-erp-system" target="id-app-service-order-mgmt"/>
    <element xsi:type="archimate:ServingRelationship" id="id-rel-wms-serves-warehouse-mgmt" source="id-app-wms" target="id-app-service-warehouse-mgmt"/>
    <element xsi:type="archimate:ServingRelationship" id="id-rel-tms-serves-transport-mgmt" source="id-app-tms" target="id-app-service-transport-mgmt"/>
    
    <!-- Application Realization Relations -->
    <element xsi:type="archimate:RealizationRelationship" id="id-rel-order-mgmt-realizes-process" source="id-app-service-order-mgmt" target="id-process-process-order"/>
    <element xsi:type="archimate:RealizationRelationship" id="id-rel-warehouse-mgmt-realizes-check" source="id-app-service-warehouse-mgmt" target="id-process-check-inventory"/>
    <element xsi:type="archimate:RealizationRelationship" id="id-rel-transport-mgmt-realizes-plan" source="id-app-service-transport-mgmt" target="id-process-plan-transport"/>
    
    <!-- Data Access Relations -->
    <element xsi:type="archimate:AccessRelationship" id="id-rel-erp-accesses-order-data" source="id-app-erp-system" target="id-data-order-data"/>
    <element xsi:type="archimate:AccessRelationship" id="id-rel-wms-accesses-inventory-data" source="id-app-wms" target="id-data-inventory-data"/>
    <element xsi:type="archimate:AccessRelationship" id="id-rel-crm-accesses-customer-data" source="id-app-crm" target="id-data-customer-data"/>
    <element xsi:type="archimate:AccessRelationship" id="id-rel-tms-accesses-transport-data" source="id-app-tms" target="id-data-transport-data"/>
    
    <!-- Technology Relations -->
    <element xsi:type="archimate:RealizationRelationship" id="id-rel-app-server-realizes-erp" source="id-node-app-server" target="id-app-erp-system"/>
    <element xsi:type="archimate:RealizationRelationship" id="id-rel-app-server-realizes-wms" source="id-node-app-server" target="id-app-wms"/>
    <element xsi:type="archimate:RealizationRelationship" id="id-rel-db-server-realizes-database-service" source="id-node-db-server" target="id-tech-service-database"/>
    
    <!-- Motivation Relations -->
    <element xsi:type="archimate:InfluenceRelationship" id="id-rel-driver-influences-goal" source="id-driver-operational-efficiency" target="id-goal-efficient-logistics"/>
    <element xsi:type="archimate:RealizationRelationship" id="id-rel-goal-realizes-outcome" source="id-goal-efficient-logistics" target="id-outcome-customer-satisfaction"/>
  </folder>
  <folder name="Views" id="id-views-folder" type="diagrams">
    <element xsi:type="archimate:ArchimateDiagramModel" name="Logistix BV - Business Process View" id="id-view-business-process">
      <!-- Business Actors -->
      <child xsi:type="archimate:DiagramObject" id="id-diagram-customer" archimateElement="id-actor-customer">
        <bounds x="50" y="50" width="120" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-customer-order" source="id-diagram-customer" target="id-diagram-receive-order" archimateRelationship="id-rel-customer-triggers-order"/>
      </child>

      <!-- Business Processes -->
      <child xsi:type="archimate:DiagramObject" id="id-diagram-receive-order" targetConnections="id-conn-customer-order" archimateElement="id-process-receive-order">
        <bounds x="50" y="150" width="120" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-receive-process" source="id-diagram-receive-order" target="id-diagram-process-order" archimateRelationship="id-rel-receive-triggers-process"/>
      </child>

      <child xsi:type="archimate:DiagramObject" id="id-diagram-process-order" targetConnections="id-conn-receive-process" archimateElement="id-process-process-order">
        <bounds x="200" y="150" width="120" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-process-check" source="id-diagram-process-order" target="id-diagram-check-inventory" archimateRelationship="id-rel-process-triggers-check"/>
      </child>

      <child xsi:type="archimate:DiagramObject" id="id-diagram-check-inventory" targetConnections="id-conn-process-check" archimateElement="id-process-check-inventory">
        <bounds x="350" y="150" width="120" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-check-pick" source="id-diagram-check-inventory" target="id-diagram-pick-goods" archimateRelationship="id-rel-check-triggers-pick"/>
      </child>

      <child xsi:type="archimate:DiagramObject" id="id-diagram-pick-goods" targetConnections="id-conn-check-pick" archimateElement="id-process-pick-goods">
        <bounds x="500" y="150" width="120" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-pick-pack" source="id-diagram-pick-goods" target="id-diagram-pack-goods" archimateRelationship="id-rel-pick-triggers-pack"/>
      </child>

      <child xsi:type="archimate:DiagramObject" id="id-diagram-pack-goods" targetConnections="id-conn-pick-pack" archimateElement="id-process-pack-goods">
        <bounds x="650" y="150" width="120" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-pack-plan" source="id-diagram-pack-goods" target="id-diagram-plan-transport" archimateRelationship="id-rel-pack-triggers-plan"/>
      </child>

      <child xsi:type="archimate:DiagramObject" id="id-diagram-plan-transport" targetConnections="id-conn-pack-plan" archimateElement="id-process-plan-transport">
        <bounds x="500" y="250" width="120" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-plan-ship" source="id-diagram-plan-transport" target="id-diagram-ship-goods" archimateRelationship="id-rel-plan-triggers-ship"/>
      </child>

      <child xsi:type="archimate:DiagramObject" id="id-diagram-ship-goods" targetConnections="id-conn-plan-ship" archimateElement="id-process-ship-goods">
        <bounds x="350" y="250" width="120" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-ship-track" source="id-diagram-ship-goods" target="id-diagram-track-delivery" archimateRelationship="id-rel-ship-triggers-track"/>
      </child>

      <child xsi:type="archimate:DiagramObject" id="id-diagram-track-delivery" targetConnections="id-conn-ship-track" archimateElement="id-process-track-delivery">
        <bounds x="200" y="250" width="120" height="55"/>
      </child>

      <!-- Application Components -->
      <child xsi:type="archimate:DiagramObject" id="id-diagram-erp" archimateElement="id-app-erp-system">
        <bounds x="50" y="350" width="150" height="75"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-erp-order-mgmt" source="id-diagram-erp" target="id-diagram-order-mgmt-service" archimateRelationship="id-rel-erp-serves-order-mgmt"/>
      </child>

      <child xsi:type="archimate:DiagramObject" id="id-diagram-wms" archimateElement="id-app-wms">
        <bounds x="250" y="350" width="150" height="75"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-wms-warehouse-mgmt" source="id-diagram-wms" target="id-diagram-warehouse-mgmt-service" archimateRelationship="id-rel-wms-serves-warehouse-mgmt"/>
      </child>

      <child xsi:type="archimate:DiagramObject" id="id-diagram-tms" archimateElement="id-app-tms">
        <bounds x="450" y="350" width="150" height="75"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-tms-transport-mgmt" source="id-diagram-tms" target="id-diagram-transport-mgmt-service" archimateRelationship="id-rel-tms-serves-transport-mgmt"/>
      </child>

      <!-- Application Services -->
      <child xsi:type="archimate:DiagramObject" id="id-diagram-order-mgmt-service" targetConnections="id-conn-erp-order-mgmt" archimateElement="id-app-service-order-mgmt">
        <bounds x="75" y="450" width="100" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-order-mgmt-realizes" source="id-diagram-order-mgmt-service" target="id-diagram-process-order" archimateRelationship="id-rel-order-mgmt-realizes-process"/>
      </child>

      <child xsi:type="archimate:DiagramObject" id="id-diagram-warehouse-mgmt-service" targetConnections="id-conn-wms-warehouse-mgmt" archimateElement="id-app-service-warehouse-mgmt">
        <bounds x="275" y="450" width="100" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-warehouse-mgmt-realizes" source="id-diagram-warehouse-mgmt-service" target="id-diagram-check-inventory" archimateRelationship="id-rel-warehouse-mgmt-realizes-check"/>
      </child>

      <child xsi:type="archimate:DiagramObject" id="id-diagram-transport-mgmt-service" targetConnections="id-conn-tms-transport-mgmt" archimateElement="id-app-service-transport-mgmt">
        <bounds x="475" y="450" width="100" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-transport-mgmt-realizes" source="id-diagram-transport-mgmt-service" target="id-diagram-plan-transport" archimateRelationship="id-rel-transport-mgmt-realizes-plan"/>
      </child>
    </element>
  </folder>
</archimate:model>
