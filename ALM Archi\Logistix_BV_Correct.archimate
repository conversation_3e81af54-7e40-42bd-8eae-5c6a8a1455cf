<?xml version="1.0" encoding="UTF-8"?>
<archimate:model xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:archimate="http://www.archimatetool.com/archimate" name="Logistix BV - E-fulfilment en Distributie" id="id-logistix-model" version="5.0.0">
  <folder name="Strategy" id="id-strategy-folder" type="strategy"/>
  <folder name="Business" id="id-business-folder" type="business">
    <element xsi:type="archimate:BusinessActor" name="Grote klant" id="id-actor-grote-klant"/>
    <element xsi:type="archimate:BusinessActor" name="Kleine klant" id="id-actor-kleine-klant"/>
    <element xsi:type="archimate:BusinessActor" name="Logistiek medewerker" id="id-actor-logistiek-medewerker"/>
    
    <element xsi:type="archimate:BusinessRole" name="Orderverwerker" id="id-role-orderverwerker"/>
    
    <element xsi:type="archimate:BusinessService" name="E-fulfilment dienstverlening" id="id-service-efulfilment"/>
    
    <element xsi:type="archimate:BusinessProcess" name="Order verwerken" id="id-process-order-verwerken"/>
    <element xsi:type="archimate:BusinessProcess" name="Order invoeren" id="id-process-order-invoeren"/>
    <element xsi:type="archimate:BusinessProcess" name="Klant controleren" id="id-process-klant-controleren"/>
    <element xsi:type="archimate:BusinessProcess" name="Voorraad check" id="id-process-voorraad-check"/>
    <element xsi:type="archimate:BusinessProcess" name="Picking &amp; Packing" id="id-process-picking-packing"/>
    <element xsi:type="archimate:BusinessProcess" name="Verzendlabel maken" id="id-process-verzendlabel-maken"/>
    <element xsi:type="archimate:BusinessProcess" name="Tracking kopiëren" id="id-process-tracking-kopieren"/>
    <element xsi:type="archimate:BusinessProcess" name="Klantcommunicatie" id="id-process-klantcommunicatie"/>
  </folder>
  <folder name="Application" id="id-application-folder" type="application">
    <element xsi:type="archimate:ApplicationService" name="Orderverwerking" id="id-app-service-orderverwerking"/>
    
    <element xsi:type="archimate:ApplicationComponent" name="ERP (cloud)" id="id-app-erp"/>
    <element xsi:type="archimate:ApplicationComponent" name="WMS (on-premise)" id="id-app-wms"/>
    <element xsi:type="archimate:ApplicationComponent" name="TMS (standalone)" id="id-app-tms"/>
    <element xsi:type="archimate:ApplicationComponent" name="CRM (losstaand)" id="id-app-crm"/>
    
    <element xsi:type="archimate:DataObject" name="Ordergegevens" id="id-data-ordergegevens"/>
    <element xsi:type="archimate:DataObject" name="Klantgegevens" id="id-data-klantgegevens"/>
    <element xsi:type="archimate:DataObject" name="Voorraadgegevens" id="id-data-voorraadgegevens"/>
    <element xsi:type="archimate:DataObject" name="Transportgegevens" id="id-data-transportgegevens"/>
  </folder>
  <folder name="Technology &amp; Physical" id="id-technology-folder" type="technology">
    <element xsi:type="archimate:Node" name="Cloud platform" id="id-node-cloud"/>
    <element xsi:type="archimate:Node" name="On-premise server" id="id-node-onpremise"/>
    <element xsi:type="archimate:CommunicationNetwork" name="Netwerk (internet)" id="id-network-internet"/>
  </folder>
  <folder name="Motivation" id="id-motivation-folder" type="motivation"/>
  <folder name="Implementation &amp; Migration" id="id-implementation-folder" type="implementation_migration"/>
  <folder name="Other" id="id-other-folder" type="other"/>
  <folder name="Relations" id="id-relations-folder" type="relations">
    <!-- Business Actor to Process -->
    <element xsi:type="archimate:TriggeringRelationship" id="id-rel-grote-klant-order" source="id-actor-grote-klant" target="id-process-order-verwerken"/>
    <element xsi:type="archimate:TriggeringRelationship" id="id-rel-kleine-klant-order" source="id-actor-kleine-klant" target="id-process-order-verwerken"/>
    <element xsi:type="archimate:AssignmentRelationship" id="id-rel-logistiek-orderverwerker" source="id-actor-logistiek-medewerker" target="id-role-orderverwerker"/>
    <element xsi:type="archimate:ServingRelationship" id="id-rel-orderverwerker-process" source="id-role-orderverwerker" target="id-process-order-verwerken"/>
    
    <!-- Process Composition (sub-processes) -->
    <element xsi:type="archimate:CompositionRelationship" id="id-rel-order-invoeren-comp" source="id-process-order-verwerken" target="id-process-order-invoeren"/>
    <element xsi:type="archimate:CompositionRelationship" id="id-rel-klant-controleren-comp" source="id-process-order-verwerken" target="id-process-klant-controleren"/>
    <element xsi:type="archimate:CompositionRelationship" id="id-rel-voorraad-check-comp" source="id-process-order-verwerken" target="id-process-voorraad-check"/>
    <element xsi:type="archimate:CompositionRelationship" id="id-rel-picking-packing-comp" source="id-process-order-verwerken" target="id-process-picking-packing"/>
    <element xsi:type="archimate:CompositionRelationship" id="id-rel-verzendlabel-comp" source="id-process-order-verwerken" target="id-process-verzendlabel-maken"/>
    <element xsi:type="archimate:CompositionRelationship" id="id-rel-tracking-comp" source="id-process-order-verwerken" target="id-process-tracking-kopieren"/>
    
    <!-- Application Realization of Processes -->
    <element xsi:type="archimate:RealizationRelationship" id="id-rel-erp-order-invoeren" source="id-app-erp" target="id-process-order-invoeren"/>
    <element xsi:type="archimate:RealizationRelationship" id="id-rel-erp-klant-controleren" source="id-app-erp" target="id-process-klant-controleren"/>
    <element xsi:type="archimate:RealizationRelationship" id="id-rel-wms-voorraad-check" source="id-app-wms" target="id-process-voorraad-check"/>
    <element xsi:type="archimate:RealizationRelationship" id="id-rel-wms-picking-packing" source="id-app-wms" target="id-process-picking-packing"/>
    <element xsi:type="archimate:RealizationRelationship" id="id-rel-tms-verzendlabel" source="id-app-tms" target="id-process-verzendlabel-maken"/>
    <element xsi:type="archimate:RealizationRelationship" id="id-rel-tms-tracking" source="id-app-tms" target="id-process-tracking-kopieren"/>
    <element xsi:type="archimate:RealizationRelationship" id="id-rel-crm-klantcommunicatie" source="id-app-crm" target="id-process-klantcommunicatie"/>
    
    <!-- Technology Realization -->
    <element xsi:type="archimate:RealizationRelationship" id="id-rel-cloud-erp" source="id-node-cloud" target="id-app-erp"/>
    <element xsi:type="archimate:RealizationRelationship" id="id-rel-onpremise-wms" source="id-node-onpremise" target="id-app-wms"/>
    <element xsi:type="archimate:RealizationRelationship" id="id-rel-onpremise-tms" source="id-node-onpremise" target="id-app-tms"/>
    <element xsi:type="archimate:RealizationRelationship" id="id-rel-onpremise-crm" source="id-node-onpremise" target="id-app-crm"/>
    
    <!-- Network connections -->
    <element xsi:type="archimate:AssociationRelationship" id="id-rel-cloud-network" source="id-node-cloud" target="id-network-internet"/>
    <element xsi:type="archimate:AssociationRelationship" id="id-rel-onpremise-network" source="id-node-onpremise" target="id-network-internet"/>
    
    <!-- Data Access -->
    <element xsi:type="archimate:AccessRelationship" id="id-rel-erp-ordergegevens" source="id-app-erp" target="id-data-ordergegevens"/>
    <element xsi:type="archimate:AccessRelationship" id="id-rel-erp-klantgegevens" source="id-app-erp" target="id-data-klantgegevens"/>
    <element xsi:type="archimate:AccessRelationship" id="id-rel-wms-voorraadgegevens" source="id-app-wms" target="id-data-voorraadgegevens"/>
    <element xsi:type="archimate:AccessRelationship" id="id-rel-tms-transportgegevens" source="id-app-tms" target="id-data-transportgegevens"/>
    
    <!-- Business Service Realization -->
    <element xsi:type="archimate:RealizationRelationship" id="id-rel-process-service" source="id-process-order-verwerken" target="id-service-efulfilment"/>
  </folder>
</archimate:model>
